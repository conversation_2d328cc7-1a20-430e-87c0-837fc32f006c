//================= Hercules Database =====================================
//=       _   _                     _
//=      | | | |                   | |
//=      | |_| | ___ _ __ ___ _   _| | ___  ___
//=      |  _  |/ _ \ '__/ __| | | | |/ _ \/ __|
//=      | | | |  __/ | | (__| |_| | |  __/\__ \
//=      \_| |_/\___|_|  \___|\__,_|_|\___||___/
//================= License ===============================================
//= This file is part of Hercules.
//= http://herc.ws - http://github.com/HerculesWS/Hercules
//=
//= Copyright (C) 2013-2015  Hercules Dev Team
//=
//= Hercules is free software: you can redistribute it and/or modify
//= it under the terms of the GNU General Public License as published by
//= the Free Software Foundation, either version 3 of the License, or
//= (at your option) any later version.
//=
//= This program is distributed in the hope that it will be useful,
//= but WITHOUT ANY WARRANTY; without even the implied warranty of
//= MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//= GNU General Public License for more details.
//=
//= You should have received a copy of the GNU General Public License
//= along with this program.  If not, see <http://www.gnu.org/licenses/>.
//================= More Information ======================================
// http://herc.ws/board/topic/1188-skill-tree-db-redesign/
//=========================================================================

/**************************************************************************
 ************* Entry structure ********************************************
 **************************************************************************
Job_Name: { // Job names as in src/map/pc.c (they are hardcoded at the moment so if you want to add a new job you should add it there)
	inherit: ( "Other_Job_Name" ); // Base job from which this job will inherit its skill tree. NV_TRICKDEAD inheritance is skipped for non-novices from the source

	skills: { // SKILL_NAMEs come from the Name (16th column) value in db/pre-re/skill_db.txt
		SKILL_NAME1: Max_Level // Use this for skills that don't have other skill prerequisite; Max_Level is a numeric value that should match your client side files
		SKILL_NAME2: { // Use this for skills which have other skills as prerequisites
			MaxLevel: Max_Level // Max_Level is a numeric value that should match your client side files
			SKILL_NAME_PREREQUISITE: Level_Prerequisite // The prerequisite skill and min level for having this skill available. Should also match your client side files
			SKILL_NAME_PREREQUISITE2: Level_Prerequisite2 // You can add as many prerequisite skills as you want. Minimum of 1 if you add a skill this way
	}
}
**************************************************************************/

Novice: {
	skills: {
		NV_BASIC: 9
		NV_FIRSTAID: 1
		NV_TRICKDEAD: 1
		WE_BABY: 1
		WE_CALLPARENT: 1
		WE_CALLBABY: 1
		ALL_INCCARRY: 1
		ALL_PICKUP: 1
	}
}
Swordsman: {
	inherit: ( "Novice" );

	skills: {
		SM_SWORD: 10
		SM_TWOHAND: {
			MaxLevel: 10
			SM_SWORD: 1
		}
		SM_RECOVERY: 10
		SM_BASH: 10
		SM_PROVOKE: 10
		SM_MAGNUM: {
			MaxLevel: 10
			SM_BASH: 5
		}
		SM_ENDURE: {
			MaxLevel: 10
			SM_PROVOKE: 5
		}
		SM_MOVINGRECOVERY: 1
		SM_FATALBLOW: 1
		SM_AUTOBERSERK: 1
	}
}
Magician: {
	inherit: ( "Novice" );

	skills: {
		MG_SRECOVERY: 10
		MG_SIGHT: 1
		MG_NAPALMBEAT: 10
		MG_SAFETYWALL: {
			MaxLevel: 10
			MG_NAPALMBEAT: 7
			MG_SOULSTRIKE: 5
		}
		MG_SOULSTRIKE: {
			MaxLevel: 10
			MG_NAPALMBEAT: 4
		}
		MG_COLDBOLT: 10
		MG_FROSTDIVER: {
			MaxLevel: 10
			MG_COLDBOLT: 5
		}
		MG_STONECURSE: 10
		MG_FIREBALL: {
			MaxLevel: 10
			MG_FIREBOLT: 4
		}
		MG_FIREWALL: {
			MaxLevel: 10
			MG_FIREBALL: 5
			MG_SIGHT: 1
		}
		MG_FIREBOLT: 10
		MG_LIGHTNINGBOLT: 10
		MG_THUNDERSTORM: {
			MaxLevel: 10
			MG_LIGHTNINGBOLT: 4
		}
		MG_ENERGYCOAT: 1
	}
}
Archer: {
	inherit: ( "Novice" );

	skills: {
		AC_OWL: 10
		AC_VULTURE: {
			MaxLevel: 10
			AC_OWL: 3
		}
		AC_CONCENTRATION: {
			MaxLevel: 10
			AC_VULTURE: 1
		}
		AC_DOUBLE: 10
		AC_SHOWER: {
			MaxLevel: 10
			AC_DOUBLE: 5
		}
		AC_MAKINGARROW: 1
		AC_CHARGEARROW: 1
	}
}
Acolyte: {
	inherit: ( "Novice" );

	skills: {
		AL_DP: 10
		AL_DEMONBANE: {
			MaxLevel: 10
			AL_DP: 3
		}
		AL_RUWACH: 1
		AL_PNEUMA: {
			MaxLevel: 1
			AL_WARP: 4
		}
		AL_TELEPORT: {
			MaxLevel: 2
			AL_RUWACH: 1
		}
		AL_WARP: {
			MaxLevel: 4
			AL_TELEPORT: 2
		}
		AL_HEAL: 10
		AL_INCAGI: {
			MaxLevel: 10
			AL_HEAL: 3
		}
		AL_DECAGI: {
			MaxLevel: 10
			AL_INCAGI: 1
		}
		AL_HOLYWATER: 1
		AL_CRUCIS: {
			MaxLevel: 10
			AL_DEMONBANE: 3
		}
		AL_ANGELUS: {
			MaxLevel: 10
			AL_DP: 3
		}
		AL_BLESSING: {
			MaxLevel: 10
			AL_DP: 5
		}
		AL_CURE: {
			MaxLevel: 1
			AL_HEAL: 2
		}
		AL_HOLYLIGHT: 1
	}
}
Merchant: {
	inherit: ( "Novice" );

	skills: {
		MC_INCCARRY: 10
		MC_DISCOUNT: {
			MaxLevel: 10
			MC_INCCARRY: 3
		}
		MC_OVERCHARGE: {
			MaxLevel: 10
			MC_DISCOUNT: 3
		}
		MC_PUSHCART: {
			MaxLevel: 10
			MC_INCCARRY: 5
		}
		MC_IDENTIFY: 1
		MC_VENDING: {
			MaxLevel: 10
			MC_PUSHCART: 3
		}
		MC_MAMMONITE: 10
		MC_CARTREVOLUTION: 1
		MC_CHANGECART: 1
		MC_CARTDECORATE: 1
		MC_LOUD: 1
		ALL_BUYING_STORE: {
			MaxLevel: 1
			MC_VENDING: 1
		}
	}
}
Thief: {
	inherit: ( "Novice" );

	skills: {
		TF_DOUBLE: 10
		TF_MISS: 10
		TF_STEAL: 10
		TF_HIDING: {
			MaxLevel: 10
			TF_STEAL: 5
		}
		TF_POISON: 10
		TF_DETOXIFY: {
			MaxLevel: 1
			TF_POISON: 3
		}
		TF_SPRINKLESAND: 1
		TF_BACKSLIDING: 1
		TF_PICKSTONE: 1
		TF_THROWSTONE: 1
	}
}
Knight: {
	inherit: ( "Swordsman" );

	skills: {
		KN_SPEARMASTERY: 10
		KN_PIERCE: {
			MaxLevel: 10
			KN_SPEARMASTERY: 1
		}
		KN_BRANDISHSPEAR: {
			MaxLevel: 10
			KN_RIDING: 1
			KN_SPEARSTAB: 3
		}
		KN_SPEARSTAB: {
			MaxLevel: 10
			KN_PIERCE: 5
		}
		KN_SPEARBOOMERANG: {
			MaxLevel: 5
			KN_PIERCE: 3
		}
		KN_TWOHANDQUICKEN: {
			MaxLevel: 10
			SM_TWOHAND: 1
		}
		KN_AUTOCOUNTER: {
			MaxLevel: 5
			SM_TWOHAND: 1
		}
		KN_BOWLINGBASH: {
			MaxLevel: 10
			SM_BASH: 10
			SM_MAGNUM: 3
			SM_TWOHAND: 5
			KN_TWOHANDQUICKEN: 10
			KN_AUTOCOUNTER: 5
		}
		KN_RIDING: {
			MaxLevel: 1
			SM_ENDURE: 1
		}
		KN_CAVALIERMASTERY: {
			MaxLevel: 5
			KN_RIDING: 1
		}
		KN_CHARGEATK: 1
		KN_ONEHAND: {
			MaxLevel: 1
			KN_TWOHANDQUICKEN: 10
		}
	}
}
Priest: {
	inherit: ( "Acolyte" );

	skills: {
		MG_SRECOVERY: 10
		MG_SAFETYWALL: {
			MaxLevel: 10
			PR_ASPERSIO: 4
			PR_SANCTUARY: 3
		}
		ALL_RESURRECTION: {
			MaxLevel: 4
			PR_STRECOVERY: 1
			MG_SRECOVERY: 4
		}
		PR_MACEMASTERY: 10
		PR_IMPOSITIO: 5
		PR_SUFFRAGIUM: {
			MaxLevel: 3
			PR_IMPOSITIO: 2
		}
		PR_ASPERSIO: {
			MaxLevel: 5
			AL_HOLYWATER: 1
			PR_IMPOSITIO: 3
		}
		PR_BENEDICTIO: {
			MaxLevel: 5
			PR_GLORIA: 3
			PR_ASPERSIO: 5
		}
		PR_SANCTUARY: {
			MaxLevel: 10
			AL_HEAL: 1
		}
		PR_SLOWPOISON: 4
		PR_STRECOVERY: 1
		PR_KYRIE: {
			MaxLevel: 10
			AL_ANGELUS: 2
		}
		PR_MAGNIFICAT: 5
		PR_GLORIA: {
			MaxLevel: 5
			PR_KYRIE: 4
			PR_MAGNIFICAT: 3
		}
		PR_LEXDIVINA: {
			MaxLevel: 10
			AL_RUWACH: 1
		}
		PR_TURNUNDEAD: {
			MaxLevel: 10
			ALL_RESURRECTION: 1
			PR_LEXDIVINA: 3
		}
		PR_LEXAETERNA: {
			MaxLevel: 1
			PR_LEXDIVINA: 5
		}
		PR_MAGNUS: {
			MaxLevel: 10
			MG_SAFETYWALL: 1
			PR_LEXAETERNA: 1
			PR_TURNUNDEAD: 3
		}
		PR_REDEMPTIO: 1
	}
}
Wizard: {
	inherit: ( "Magician" );

	skills: {
		WZ_FIREPILLAR: {
			MaxLevel: 10
			MG_FIREWALL: 1
		}
		WZ_SIGHTRASHER: {
			MaxLevel: 10
			MG_LIGHTNINGBOLT: 1
			MG_SIGHT: 1
		}
		WZ_METEOR: {
			MaxLevel: 10
			WZ_SIGHTRASHER: 2
			MG_THUNDERSTORM: 1
		}
		WZ_JUPITEL: {
			MaxLevel: 10
			MG_NAPALMBEAT: 1
			MG_LIGHTNINGBOLT: 1
		}
		WZ_VERMILION: {
			MaxLevel: 10
			MG_THUNDERSTORM: 1
			WZ_JUPITEL: 5
		}
		WZ_WATERBALL: {
			MaxLevel: 5
			MG_COLDBOLT: 1
			MG_LIGHTNINGBOLT: 1
		}
		WZ_ICEWALL: {
			MaxLevel: 10
			MG_STONECURSE: 1
			MG_FROSTDIVER: 1
		}
		WZ_FROSTNOVA: {
			MaxLevel: 10
			WZ_ICEWALL: 1
		}
		WZ_STORMGUST: {
			MaxLevel: 10
			MG_FROSTDIVER: 1
			WZ_JUPITEL: 3
		}
		WZ_EARTHSPIKE: {
			MaxLevel: 5
			MG_STONECURSE: 1
		}
		WZ_HEAVENDRIVE: {
			MaxLevel: 5
			WZ_EARTHSPIKE: 3
		}
		WZ_QUAGMIRE: {
			MaxLevel: 5
			WZ_HEAVENDRIVE: 1
		}
		WZ_ESTIMATION: 1
		WZ_SIGHTBLASTER: 1
	}
}
Blacksmith: {
	inherit: ( "Merchant" );

	skills: {
		BS_IRON: 5
		BS_STEEL: {
			MaxLevel: 5
			BS_IRON: 1
		}
		BS_ENCHANTEDSTONE: {
			MaxLevel: 5
			BS_IRON: 1
		}
		BS_ORIDEOCON: {
			MaxLevel: 5
			BS_ENCHANTEDSTONE: 1
		}
		BS_DAGGER: 3
		BS_SWORD: {
			MaxLevel: 3
			BS_DAGGER: 1
		}
		BS_TWOHANDSWORD: {
			MaxLevel: 3
			BS_SWORD: 1
		}
		BS_AXE: {
			MaxLevel: 3
			BS_SWORD: 2
		}
		BS_MACE: {
			MaxLevel: 3
			BS_KNUCKLE: 1
		}
		BS_KNUCKLE: {
			MaxLevel: 3
			BS_DAGGER: 1
		}
		BS_SPEAR: {
			MaxLevel: 3
			BS_DAGGER: 2
		}
		BS_HILTBINDING: 1
		BS_FINDINGORE: {
			MaxLevel: 1
			BS_STEEL: 1
			BS_HILTBINDING: 1
		}
		BS_WEAPONRESEARCH: {
			MaxLevel: 10
			BS_HILTBINDING: 1
		}
		BS_REPAIRWEAPON: {
			MaxLevel: 1
			BS_WEAPONRESEARCH: 1
		}
		BS_SKINTEMPER: 5
		BS_HAMMERFALL: 5
		BS_ADRENALINE: {
			MaxLevel: 5
			BS_HAMMERFALL: 2
		}
		BS_WEAPONPERFECT: {
			MaxLevel: 5
			BS_WEAPONRESEARCH: 2
			BS_ADRENALINE: 2
		}
		BS_OVERTHRUST: {
			MaxLevel: 5
			BS_ADRENALINE: 3
		}
		BS_MAXIMIZE: {
			MaxLevel: 5
			BS_WEAPONPERFECT: 3
			BS_OVERTHRUST: 2
		}
		BS_UNFAIRLYTRICK: 1
		BS_GREED: 1
		BS_ADRENALINE2: {
			MaxLevel: 1
			BS_ADRENALINE: 5
		}
	}
}
Hunter: {
	inherit: ( "Archer" );

	skills: {
		HT_SKIDTRAP: 5
		HT_LANDMINE: 5
		HT_ANKLESNARE: {
			MaxLevel: 5
			HT_SKIDTRAP: 1
		}
		HT_SHOCKWAVE: {
			MaxLevel: 5
			HT_ANKLESNARE: 1
		}
		HT_SANDMAN: {
			MaxLevel: 5
			HT_FLASHER: 1
		}
		HT_FLASHER: {
			MaxLevel: 5
			HT_SKIDTRAP: 1
		}
		HT_FREEZINGTRAP: {
			MaxLevel: 5
			HT_FLASHER: 1
		}
		HT_BLASTMINE: {
			MaxLevel: 5
			HT_LANDMINE: 1
			HT_SANDMAN: 1
			HT_FREEZINGTRAP: 1
		}
		HT_CLAYMORETRAP: {
			MaxLevel: 5
			HT_SHOCKWAVE: 1
			HT_BLASTMINE: 1
		}
		HT_REMOVETRAP: {
			MaxLevel: 1
			HT_LANDMINE: 1
		}
		HT_TALKIEBOX: {
			MaxLevel: 1
			HT_SHOCKWAVE: 1
			HT_REMOVETRAP: 1
		}
		HT_BEASTBANE: 10
		HT_FALCON: {
			MaxLevel: 1
			HT_BEASTBANE: 1
		}
		HT_STEELCROW: {
			MaxLevel: 10
			HT_BLITZBEAT: 5
		}
		HT_BLITZBEAT: {
			MaxLevel: 5
			HT_FALCON: 1
		}
		HT_DETECTING: {
			MaxLevel: 4
			AC_CONCENTRATION: 1
			HT_FALCON: 1
		}
		HT_SPRINGTRAP: {
			MaxLevel: 5
			HT_REMOVETRAP: 1
			HT_FALCON: 1
		}
		HT_PHANTASMIC: 1
		HT_POWER: {
			MaxLevel: 1
			AC_DOUBLE: 10
		}
	}
}
Assassin: {
	inherit: ( "Thief" );

	skills: {
		AS_RIGHT: 5
		AS_LEFT: {
			MaxLevel: 5
			AS_RIGHT: 2
		}
		AS_KATAR: 10
		AS_CLOAKING: {
			MaxLevel: 10
			TF_HIDING: 2
		}
		AS_SONICBLOW: {
			MaxLevel: 10
			AS_KATAR: 4
		}
		AS_GRIMTOOTH: {
			MaxLevel: 5
			AS_CLOAKING: 2
			AS_SONICBLOW: 5
		}
		AS_ENCHANTPOISON: {
			MaxLevel: 10
			TF_POISON: 1
		}
		AS_POISONREACT: {
			MaxLevel: 10
			AS_ENCHANTPOISON: 3
		}
		AS_VENOMDUST: {
			MaxLevel: 10
			AS_ENCHANTPOISON: 5
		}
		AS_SPLASHER: {
			MaxLevel: 10
			AS_POISONREACT: 5
			AS_VENOMDUST: 5
		}
		AS_SONICACCEL: 1
		AS_VENOMKNIFE: 1
	}
}
Crusader: {
	inherit: ( "Swordsman" );

	skills: {
		KN_RIDING: {
			MaxLevel: 1
			SM_ENDURE: 1
		}
		KN_CAVALIERMASTERY: {
			MaxLevel: 5
			KN_RIDING: 1
		}
		KN_SPEARMASTERY: 10
		AL_CURE: {
			MaxLevel: 1
			CR_TRUST: 5
		}
		AL_DP: {
			MaxLevel: 10
			AL_CURE: 1
		}
		AL_DEMONBANE: {
			MaxLevel: 10
			AL_DP: 3
		}
		AL_HEAL: {
			MaxLevel: 10
			AL_DEMONBANE: 5
			CR_TRUST: 10
		}
		CR_TRUST: 10
		CR_AUTOGUARD: 10
		CR_SHIELDCHARGE: {
			MaxLevel: 5
			CR_AUTOGUARD: 5
		}
		CR_SHIELDBOOMERANG: {
			MaxLevel: 5
			CR_SHIELDCHARGE: 3
		}
		CR_REFLECTSHIELD: {
			MaxLevel: 10
			CR_SHIELDBOOMERANG: 3
		}
		CR_HOLYCROSS: {
			MaxLevel: 10
			CR_TRUST: 7
		}
		CR_GRANDCROSS: {
			MaxLevel: 10
			CR_HOLYCROSS: 6
			CR_TRUST: 10
		}
		CR_DEVOTION: {
			MaxLevel: 5
			CR_REFLECTSHIELD: 5
			CR_GRANDCROSS: 4
		}
		CR_PROVIDENCE: {
			MaxLevel: 5
			AL_DP: 5
			AL_HEAL: 5
		}
		CR_DEFENDER: {
			MaxLevel: 5
			CR_SHIELDBOOMERANG: 1
		}
		CR_SPEARQUICKEN: {
			MaxLevel: 10
			KN_SPEARMASTERY: 10
		}
		CR_SHRINK: 1
	}
}
Monk: {
	inherit: ( "Acolyte" );

	skills: {
		MO_IRONHAND: {
			MaxLevel: 10
			AL_DEMONBANE: 10
			AL_DP: 10
		}
		MO_SPIRITSRECOVERY: {
			MaxLevel: 5
			MO_BLADESTOP: 2
		}
		MO_CALLSPIRITS: {
			MaxLevel: 5
			MO_IRONHAND: 2
		}
		MO_ABSORBSPIRITS: {
			MaxLevel: 1
			MO_CALLSPIRITS: 5
		}
		MO_TRIPLEATTACK: {
			MaxLevel: 10
			MO_DODGE: 5
		}
		MO_BODYRELOCATION: {
			MaxLevel: 1
			MO_EXTREMITYFIST: 3
			MO_SPIRITSRECOVERY: 2
			MO_STEELBODY: 3
		}
		MO_DODGE: {
			MaxLevel: 10
			MO_IRONHAND: 5
			MO_CALLSPIRITS: 5
		}
		MO_INVESTIGATE: {
			MaxLevel: 5
			MO_CALLSPIRITS: 5
		}
		MO_FINGEROFFENSIVE: {
			MaxLevel: 5
			MO_INVESTIGATE: 3
		}
		MO_STEELBODY: {
			MaxLevel: 5
			MO_COMBOFINISH: 3
		}
		MO_BLADESTOP: {
			MaxLevel: 5
			MO_DODGE: 5
		}
		MO_EXPLOSIONSPIRITS: {
			MaxLevel: 5
			MO_ABSORBSPIRITS: 1
		}
		MO_EXTREMITYFIST: {
			MaxLevel: 5
			MO_EXPLOSIONSPIRITS: 3
			MO_FINGEROFFENSIVE: 3
		}
		MO_CHAINCOMBO: {
			MaxLevel: 5
			MO_TRIPLEATTACK: 5
		}
		MO_COMBOFINISH: {
			MaxLevel: 5
			MO_CHAINCOMBO: 3
		}
		MO_KITRANSLATION: 1
		MO_BALKYOUNG: 1
	}
}
Sage: {
	inherit: ( "Magician" );

	skills: {
		WZ_ESTIMATION: 1
		WZ_EARTHSPIKE: {
			MaxLevel: 5
			SA_SEISMICWEAPON: 1
		}
		WZ_HEAVENDRIVE: {
			MaxLevel: 5
			WZ_EARTHSPIKE: 1
		}
		SA_ADVANCEDBOOK: 10
		SA_CASTCANCEL: {
			MaxLevel: 5
			SA_ADVANCEDBOOK: 2
		}
		SA_MAGICROD: {
			MaxLevel: 5
			SA_ADVANCEDBOOK: 4
		}
		SA_SPELLBREAKER: {
			MaxLevel: 5
			SA_MAGICROD: 1
		}
		SA_FREECAST: {
			MaxLevel: 10
			SA_CASTCANCEL: 1
		}
		SA_AUTOSPELL: {
			MaxLevel: 10
			SA_FREECAST: 4
		}
		SA_FLAMELAUNCHER: {
			MaxLevel: 5
			MG_FIREBOLT: 1
			SA_ADVANCEDBOOK: 5
		}
		SA_FROSTWEAPON: {
			MaxLevel: 5
			MG_COLDBOLT: 1
			SA_ADVANCEDBOOK: 5
		}
		SA_LIGHTNINGLOADER: {
			MaxLevel: 5
			MG_LIGHTNINGBOLT: 1
			SA_ADVANCEDBOOK: 5
		}
		SA_SEISMICWEAPON: {
			MaxLevel: 5
			MG_STONECURSE: 1
			SA_ADVANCEDBOOK: 5
		}
		SA_DRAGONOLOGY: {
			MaxLevel: 5
			SA_ADVANCEDBOOK: 9
		}
		SA_VOLCANO: {
			MaxLevel: 5
			SA_FLAMELAUNCHER: 2
		}
		SA_DELUGE: {
			MaxLevel: 5
			SA_FROSTWEAPON: 2
		}
		SA_VIOLENTGALE: {
			MaxLevel: 5
			SA_LIGHTNINGLOADER: 2
		}
		SA_LANDPROTECTOR: {
			MaxLevel: 5
			SA_VOLCANO: 3
			SA_DELUGE: 3
			SA_VIOLENTGALE: 3
		}
		SA_DISPELL: {
			MaxLevel: 5
			SA_SPELLBREAKER: 3
		}
		SA_ABRACADABRA: {
			MaxLevel: 10
			SA_AUTOSPELL: 5
			SA_DISPELL: 1
			SA_LANDPROTECTOR: 1
		}
		SA_CREATECON: 1
		SA_ELEMENTWATER: 1
		SA_ELEMENTGROUND: 1
		SA_ELEMENTFIRE: 1
		SA_ELEMENTWIND: 1
	}
}
Rogue: {
	inherit: ( "Thief" );

	skills: {
		SM_SWORD: 10
		AC_VULTURE: 10
		AC_DOUBLE: {
			MaxLevel: 10
			AC_VULTURE: 10
		}
		HT_REMOVETRAP: {
			MaxLevel: 1
			AC_DOUBLE: 5
		}
		RG_SNATCHER: {
			MaxLevel: 10
			TF_STEAL: 1
		}
		RG_STEALCOIN: {
			MaxLevel: 10
			RG_SNATCHER: 4
		}
		RG_BACKSTAP: {
			MaxLevel: 10
			RG_STEALCOIN: 4
		}
		RG_TUNNELDRIVE: {
			MaxLevel: 5
			TF_HIDING: 1
		}
		RG_RAID: {
			MaxLevel: 5
			RG_BACKSTAP: 2
			RG_TUNNELDRIVE: 2
		}
		RG_STRIPWEAPON: {
			MaxLevel: 5
			RG_STRIPARMOR: 5
		}
		RG_STRIPSHIELD: {
			MaxLevel: 5
			RG_STRIPHELM: 5
		}
		RG_STRIPARMOR: {
			MaxLevel: 5
			RG_STRIPSHIELD: 5
		}
		RG_STRIPHELM: {
			MaxLevel: 5
			RG_STEALCOIN: 2
		}
		RG_INTIMIDATE: {
			MaxLevel: 5
			RG_BACKSTAP: 4
			RG_RAID: 5
		}
		RG_GRAFFITI: {
			MaxLevel: 1
			RG_FLAGGRAFFITI: 5
		}
		RG_FLAGGRAFFITI: {
			MaxLevel: 5
			RG_CLEANER: 1
		}
		RG_CLEANER: {
			MaxLevel: 1
			RG_GANGSTER: 1
		}
		RG_GANGSTER: {
			MaxLevel: 1
			RG_STRIPSHIELD: 3
		}
		RG_COMPULSION: {
			MaxLevel: 5
			RG_GANGSTER: 1
		}
		RG_PLAGIARISM: {
			MaxLevel: 10
			RG_INTIMIDATE: 5
		}
		RG_CLOSECONFINE: 1
	}
}
Alchemist: {
	inherit: ( "Merchant" );

	skills: {
		AM_AXEMASTERY: 10
		AM_LEARNINGPOTION: 10
		AM_PHARMACY: {
			MaxLevel: 10
			AM_LEARNINGPOTION: 5
		}
		AM_DEMONSTRATION: {
			MaxLevel: 5
			AM_PHARMACY: 4
		}
		AM_ACIDTERROR: {
			MaxLevel: 5
			AM_PHARMACY: 5
		}
		AM_POTIONPITCHER: {
			MaxLevel: 5
			AM_PHARMACY: 3
		}
		AM_CANNIBALIZE: {
			MaxLevel: 5
			AM_PHARMACY: 6
		}
		AM_SPHEREMINE: {
			MaxLevel: 5
			AM_PHARMACY: 2
		}
		AM_CP_WEAPON: {
			MaxLevel: 5
			AM_CP_ARMOR: 3
		}
		AM_CP_SHIELD: {
			MaxLevel: 5
			AM_CP_HELM: 3
		}
		AM_CP_ARMOR: {
			MaxLevel: 5
			AM_CP_SHIELD: 3
		}
		AM_CP_HELM: {
			MaxLevel: 5
			AM_PHARMACY: 2
		}
		AM_BIOETHICS: 1
		AM_CALLHOMUN: {
			MaxLevel: 1
			AM_REST: 1
		}
		AM_REST: {
			MaxLevel: 1
			AM_BIOETHICS: 1
		}
		AM_RESURRECTHOMUN: {
			MaxLevel: 5
			AM_CALLHOMUN: 1
		}
		AM_BERSERKPITCHER: 1
		AM_TWILIGHT1: {
			MaxLevel: 1
			AM_PHARMACY: 10
		}
		AM_TWILIGHT2: {
			MaxLevel: 1
			AM_PHARMACY: 10
		}
		AM_TWILIGHT3: {
			MaxLevel: 1
			AM_PHARMACY: 10
		}
	}
}
Bard: {
	inherit: ( "Archer" );

	skills: {
		BA_MUSICALLESSON: 10
		BA_MUSICALSTRIKE: {
			MaxLevel: 5
			BA_MUSICALLESSON: 3
		}
		BA_DISSONANCE: {
			MaxLevel: 5
			BA_MUSICALLESSON: 1
			BD_ADAPTATION: 1
		}
		BA_FROSTJOKER: {
			MaxLevel: 5
			BD_ENCORE: 1
		}
		BA_WHISTLE: {
			MaxLevel: 10
			BA_DISSONANCE: 3
		}
		BA_ASSASSINCROSS: {
			MaxLevel: 10
			BA_DISSONANCE: 3
		}
		BA_POEMBRAGI: {
			MaxLevel: 10
			BA_DISSONANCE: 3
		}
		BA_APPLEIDUN: {
			MaxLevel: 10
			BA_DISSONANCE: 3
		}
		BD_ADAPTATION: 1
		BD_ENCORE: {
			MaxLevel: 1
			BD_ADAPTATION: 1
		}
		BD_LULLABY: {
			MaxLevel: 1
			BA_WHISTLE: 10
		}
		BD_RICHMANKIM: {
			MaxLevel: 5
			BD_SIEGFRIED: 3
		}
		BD_ETERNALCHAOS: {
			MaxLevel: 1
			BD_ROKISWEIL: 1
		}
		BD_DRUMBATTLEFIELD: {
			MaxLevel: 5
			BA_APPLEIDUN: 10
		}
		BD_RINGNIBELUNGEN: {
			MaxLevel: 5
			BD_DRUMBATTLEFIELD: 3
		}
		BD_ROKISWEIL: {
			MaxLevel: 1
			BA_ASSASSINCROSS: 10
		}
		BD_INTOABYSS: {
			MaxLevel: 1
			BD_LULLABY: 1
		}
		BD_SIEGFRIED: {
			MaxLevel: 5
			BA_POEMBRAGI: 10
		}
		BA_PANGVOICE: 1
	}
}
Dancer: {
	inherit: ( "Archer" );

	skills: {
		DC_DANCINGLESSON: 10
		DC_THROWARROW: {
			MaxLevel: 5
			DC_DANCINGLESSON: 3
		}
		DC_UGLYDANCE: {
			MaxLevel: 5
			DC_DANCINGLESSON: 1
			BD_ADAPTATION: 1
		}
		DC_SCREAM: {
			MaxLevel: 5
			BD_ENCORE: 1
		}
		DC_HUMMING: {
			MaxLevel: 10
			DC_UGLYDANCE: 3
		}
		DC_DONTFORGETME: {
			MaxLevel: 10
			DC_UGLYDANCE: 3
		}
		DC_FORTUNEKISS: {
			MaxLevel: 10
			DC_UGLYDANCE: 3
		}
		DC_SERVICEFORYOU: {
			MaxLevel: 10
			DC_UGLYDANCE: 3
		}
		BD_ADAPTATION: 1
		BD_ENCORE: {
			MaxLevel: 1
			BD_ADAPTATION: 1
		}
		BD_LULLABY: {
			MaxLevel: 1
			DC_HUMMING: 10
		}
		BD_RICHMANKIM: {
			MaxLevel: 5
			BD_SIEGFRIED: 3
		}
		BD_ETERNALCHAOS: {
			MaxLevel: 1
			BD_ROKISWEIL: 1
		}
		BD_DRUMBATTLEFIELD: {
			MaxLevel: 5
			DC_SERVICEFORYOU: 10
		}
		BD_RINGNIBELUNGEN: {
			MaxLevel: 5
			BD_DRUMBATTLEFIELD: 3
		}
		BD_ROKISWEIL: {
			MaxLevel: 1
			DC_DONTFORGETME: 10
		}
		BD_INTOABYSS: {
			MaxLevel: 1
			BD_LULLABY: 1
		}
		BD_SIEGFRIED: {
			MaxLevel: 5
			DC_FORTUNEKISS: 10
		}
		DC_WINKCHARM: 1
	}
}
Super_Novice: {
	inherit: ( "Novice" );

	skills: {
		SM_SWORD: 10
		SM_RECOVERY: 10
		SM_BASH: 10
		SM_PROVOKE: 10
		SM_MAGNUM: {
			MaxLevel: 10
			SM_BASH: 5
		}
		SM_ENDURE: {
			MaxLevel: 10
			SM_PROVOKE: 5
		}
		MG_SRECOVERY: 10
		MG_SIGHT: 1
		MG_NAPALMBEAT: 10
		MG_SAFETYWALL: {
			MaxLevel: 10
			MG_NAPALMBEAT: 7
			MG_SOULSTRIKE: 5
		}
		MG_SOULSTRIKE: {
			MaxLevel: 10
			MG_NAPALMBEAT: 4
		}
		MG_COLDBOLT: 10
		MG_FROSTDIVER: {
			MaxLevel: 10
			MG_COLDBOLT: 5
		}
		MG_STONECURSE: 10
		MG_FIREBALL: {
			MaxLevel: 10
			MG_FIREBOLT: 4
		}
		MG_FIREWALL: {
			MaxLevel: 10
			MG_FIREBALL: 5
			MG_SIGHT: 1
		}
		MG_FIREBOLT: 10
		MG_LIGHTNINGBOLT: 10
		MG_THUNDERSTORM: {
			MaxLevel: 10
			MG_LIGHTNINGBOLT: 4
		}
		AL_DP: 10
		AL_DEMONBANE: {
			MaxLevel: 10
			AL_DP: 3
		}
		AL_RUWACH: 1
		AL_PNEUMA: {
			MaxLevel: 1
			AL_WARP: 4
		}
		AL_TELEPORT: {
			MaxLevel: 2
			AL_RUWACH: 1
		}
		AL_WARP: {
			MaxLevel: 4
			AL_TELEPORT: 2
		}
		AL_HEAL: 10
		AL_INCAGI: {
			MaxLevel: 10
			AL_HEAL: 3
		}
		AL_DECAGI: {
			MaxLevel: 10
			AL_INCAGI: 1
		}
		AL_HOLYWATER: 1
		AL_CRUCIS: {
			MaxLevel: 10
			AL_DEMONBANE: 3
		}
		AL_ANGELUS: {
			MaxLevel: 10
			AL_DP: 3
		}
		AL_BLESSING: {
			MaxLevel: 10
			AL_DP: 5
		}
		AL_CURE: {
			MaxLevel: 1
			AL_HEAL: 2
		}
		MC_INCCARRY: 10
		MC_DISCOUNT: {
			MaxLevel: 10
			MC_INCCARRY: 3
		}
		MC_OVERCHARGE: {
			MaxLevel: 10
			MC_DISCOUNT: 3
		}
		MC_PUSHCART: {
			MaxLevel: 10
			MC_INCCARRY: 5
		}
		MC_IDENTIFY: 1
		MC_VENDING: {
			MaxLevel: 10
			MC_PUSHCART: 3
		}
		MC_MAMMONITE: 10
		AC_OWL: 10
		AC_VULTURE: {
			MaxLevel: 10
			AC_OWL: 3
		}
		AC_CONCENTRATION: {
			MaxLevel: 10
			AC_VULTURE: 1
		}
		TF_DOUBLE: 10
		TF_MISS: 10
		TF_STEAL: 10
		TF_HIDING: {
			MaxLevel: 10
			TF_STEAL: 5
		}
		TF_POISON: 10
		TF_DETOXIFY: {
			MaxLevel: 1
			TF_POISON: 3
		}
		ALL_BUYING_STORE: {
			MaxLevel: 1
			MC_VENDING: 1
		}
	}
}
Gunslinger: {
	inherit: ( "Novice" );

	skills: {
		GS_GLITTERING: 5
		GS_FLING: {
			MaxLevel: 1
			GS_GLITTERING: 1
		}
		GS_TRIPLEACTION: {
			MaxLevel: 1
			GS_GLITTERING: 1
			GS_CHAINACTION: 10
		}
		GS_BULLSEYE: {
			MaxLevel: 1
			GS_GLITTERING: 5
			GS_TRACKING: 10
		}
		GS_MADNESSCANCEL: {
			MaxLevel: 1
			GS_GLITTERING: 4
			GS_GATLINGFEVER: 10
		}
		GS_ADJUSTMENT: {
			MaxLevel: 1
			GS_GLITTERING: 4
			GS_DISARM: 5
		}
		GS_INCREASING: {
			MaxLevel: 1
			GS_GLITTERING: 2
			GS_SNAKEEYE: 10
		}
		GS_MAGICALBULLET: {
			MaxLevel: 1
			GS_GLITTERING: 1
		}
		GS_CRACKER: {
			MaxLevel: 1
			GS_GLITTERING: 1
		}
		GS_SINGLEACTION: 10
		GS_SNAKEEYE: 10
		GS_CHAINACTION: {
			MaxLevel: 10
			GS_SINGLEACTION: 1
		}
		GS_TRACKING: {
			MaxLevel: 10
			GS_SINGLEACTION: 5
		}
		GS_DISARM: {
			MaxLevel: 5
			GS_TRACKING: 7
		}
		GS_PIERCINGSHOT: {
			MaxLevel: 5
			GS_TRACKING: 5
		}
		GS_RAPIDSHOWER: {
			MaxLevel: 10
			GS_CHAINACTION: 3
		}
		GS_DESPERADO: {
			MaxLevel: 10
			GS_RAPIDSHOWER: 5
		}
		GS_GATLINGFEVER: {
			MaxLevel: 10
			GS_RAPIDSHOWER: 7
			GS_DESPERADO: 5
		}
		GS_DUST: {
			MaxLevel: 10
			GS_SINGLEACTION: 5
		}
		GS_FULLBUSTER: {
			MaxLevel: 10
			GS_DUST: 3
		}
		GS_SPREADATTACK: {
			MaxLevel: 10
			GS_FULLBUSTER: 5
		}
		GS_GROUNDDRIFT: {
			MaxLevel: 10
			GS_SPREADATTACK: 7
		}
	}
}
Ninja: {
	inherit: ( "Novice" );

	skills: {
		NJ_TOBIDOUGU: 10
		NJ_SYURIKEN: {
			MaxLevel: 10
			NJ_TOBIDOUGU: 1
		}
		NJ_KUNAI: {
			MaxLevel: 5
			NJ_SYURIKEN: 5
		}
		NJ_HUUMA: {
			MaxLevel: 5
			NJ_TOBIDOUGU: 5
			NJ_KUNAI: 5
		}
		NJ_ZENYNAGE: {
			MaxLevel: 10
			NJ_TOBIDOUGU: 10
			NJ_HUUMA: 5
		}
		NJ_TATAMIGAESHI: 5
		NJ_KASUMIKIRI: {
			MaxLevel: 10
			NJ_SHADOWJUMP: 1
		}
		NJ_SHADOWJUMP: {
			MaxLevel: 5
			NJ_TATAMIGAESHI: 1
		}
		NJ_KIRIKAGE: {
			MaxLevel: 5
			NJ_KASUMIKIRI: 5
		}
		NJ_UTSUSEMI: {
			MaxLevel: 5
			NJ_SHADOWJUMP: 5
		}
		NJ_BUNSINJYUTSU: {
			MaxLevel: 10
			NJ_UTSUSEMI: 4
			NJ_KIRIKAGE: 3
			NJ_NEN: 1
		}
		NJ_NINPOU: 10
		NJ_KOUENKA: {
			MaxLevel: 10
			NJ_NINPOU: 1
		}
		NJ_KAENSIN: {
			MaxLevel: 10
			NJ_KOUENKA: 5
		}
		NJ_BAKUENRYU: {
			MaxLevel: 5
			NJ_NINPOU: 10
			NJ_KAENSIN: 7
		}
		NJ_HYOUSENSOU: {
			MaxLevel: 10
			NJ_NINPOU: 1
		}
		NJ_SUITON: {
			MaxLevel: 10
			NJ_HYOUSENSOU: 5
		}
		NJ_HYOUSYOURAKU: {
			MaxLevel: 5
			NJ_NINPOU: 10
			NJ_SUITON: 7
		}
		NJ_HUUJIN: {
			MaxLevel: 10
			NJ_NINPOU: 1
		}
		NJ_RAIGEKISAI: {
			MaxLevel: 5
			NJ_HUUJIN: 5
		}
		NJ_KAMAITACHI: {
			MaxLevel: 5
			NJ_NINPOU: 10
			NJ_RAIGEKISAI: 5
		}
		NJ_NEN: {
			MaxLevel: 5
			NJ_NINPOU: 5
		}
		NJ_ISSEN: {
			MaxLevel: 10
			NJ_TOBIDOUGU: 7
			NJ_KIRIKAGE: 5
			NJ_NEN: 1
		}
	}
}
Novice_High: {
	inherit: ( "Novice" );
}
Swordsman_High: {
	inherit: ( "Swordsman" );
}
Magician_High: {
	inherit: ( "Magician" );
}
Archer_High: {
	inherit: ( "Archer" );
}
Acolyte_High: {
	inherit: ( "Acolyte" );
}
Merchant_High: {
	inherit: ( "Merchant" );
}
Thief_High: {
	inherit: ( "Thief" );
}
Lord_Knight: {
	inherit: ( "Knight" );
	skills: {
		LK_AURABLADE: {
			MaxLevel: 5
			SM_BASH: 5
			SM_MAGNUM: 5
			SM_TWOHAND: 5
		}
		LK_PARRYING: {
			MaxLevel: 10
			SM_TWOHAND: 10
			SM_PROVOKE: 5
			KN_TWOHANDQUICKEN: 3
		}
		LK_CONCENTRATION: {
			MaxLevel: 5
			SM_RECOVERY: 5
			KN_SPEARMASTERY: 5
			KN_RIDING: 1
		}
		LK_TENSIONRELAX: {
			MaxLevel: 1
			SM_RECOVERY: 10
			SM_PROVOKE: 5
			SM_ENDURE: 3
		}
		LK_BERSERK: {
			MaxLevel: 1
			MinJobLevel: 50
		}
		LK_SPIRALPIERCE: {
			MaxLevel: 5
			KN_SPEARMASTERY: 10
			KN_PIERCE: 5
			KN_SPEARSTAB: 5
			KN_RIDING: 1
		}
		LK_HEADCRUSH: {
			MaxLevel: 5
			KN_SPEARMASTERY: 9
			KN_RIDING: 1
		}
		LK_JOINTBEAT: {
			MaxLevel: 10
			KN_SPEARMASTERY: 9
			KN_CAVALIERMASTERY: 3
			LK_HEADCRUSH: 3
		}
	}
}
High_Priest: {
	inherit: ( "Priest" );

	skills: {
		HP_ASSUMPTIO: {
			MaxLevel: 5
			AL_ANGELUS: 1
			MG_SRECOVERY: 3
			PR_IMPOSITIO: 3
		}
		HP_BASILICA: {
			MaxLevel: 5
			PR_GLORIA: 2
			MG_SRECOVERY: 1
			PR_KYRIE: 3
		}
		HP_MEDITATIO: {
			MaxLevel: 10
			PR_ASPERSIO: 3
			MG_SRECOVERY: 5
			PR_LEXDIVINA: 5
		}
		HP_MANARECHARGE: {
			MaxLevel: 5
			PR_MACEMASTERY: 10
			AL_DEMONBANE: 10
		}
	}
}
High_Wizard: {
	inherit: ( "Wizard" );

	skills: {
		HW_SOULDRAIN: {
			MaxLevel: 10
			MG_SRECOVERY: 5
			MG_SOULSTRIKE: 7
		}
		HW_MAGICCRASHER: {
			MaxLevel: 1
			MG_SRECOVERY: 1
		}
		HW_MAGICPOWER: 10
		HW_NAPALMVULCAN: {
			MaxLevel: 5
			MG_NAPALMBEAT: 5
		}
		HW_GANBANTEIN: {
			MaxLevel: 1
			WZ_ESTIMATION: 1
			WZ_ICEWALL: 1
		}
		HW_GRAVITATION: {
			MaxLevel: 5
			HW_MAGICCRASHER: 1
			HW_MAGICPOWER: 10
			WZ_QUAGMIRE: 1
		}
	}
}
Whitesmith: {
	inherit: ( "Blacksmith" );

	skills: {
		WS_MELTDOWN: {
			MaxLevel: 10
			BS_SKINTEMPER: 3
			BS_HILTBINDING: 1
			BS_WEAPONRESEARCH: 5
			BS_OVERTHRUST: 3
		}
		WS_CARTBOOST: {
			MaxLevel: 1
			MC_PUSHCART: 5
			MC_CARTREVOLUTION: 1
			MC_CHANGECART: 1
			BS_HILTBINDING: 1
		}
		WS_WEAPONREFINE: {
			MaxLevel: 10
			BS_WEAPONRESEARCH: 10
		}
		WS_CARTTERMINATION: {
			MaxLevel: 10
			MC_MAMMONITE: 10
			BS_HAMMERFALL: 5
			WS_CARTBOOST: 1
		}
		WS_OVERTHRUSTMAX: {
			MaxLevel: 5
			BS_OVERTHRUST: 5
		}
		WS_GREEDPARRY: 10
	}
}
Sniper: {
	inherit: ( "Hunter" );

	skills: {
		SN_SIGHT: {
			MaxLevel: 10
			AC_OWL: 10
			AC_VULTURE: 10
			AC_CONCENTRATION: 10
			HT_FALCON: 1
		}
		SN_FALCONASSAULT: {
			MaxLevel: 5
			HT_STEELCROW: 3
			AC_VULTURE: 5
			HT_BLITZBEAT: 5
			HT_FALCON: 1
		}
		SN_SHARPSHOOTING: {
			MaxLevel: 5
			AC_CONCENTRATION: 10
			AC_DOUBLE: 5
		}
		SN_WINDWALK: {
			MaxLevel: 10
			AC_CONCENTRATION: 9
		}
	}
}
Assassin_Cross: {
	inherit: ( "Assassin" );

	skills: {
		ASC_KATAR: {
			MaxLevel: 5
			TF_DOUBLE: 5
			AS_KATAR: 7
		}
		ASC_EDP: {
			MaxLevel: 5
			ASC_CDP: 1
		}
		ASC_BREAKER: {
			MaxLevel: 10
			TF_DOUBLE: 5
			AS_CLOAKING: 3
			AS_ENCHANTPOISON: 6
			TF_POISON: 5
		}
		ASC_METEORASSAULT: {
			MaxLevel: 10
			AS_RIGHT: 3
			AS_KATAR: 5
			AS_SONICBLOW: 5
			ASC_BREAKER: 1
		}
		ASC_CDP: {
			MaxLevel: 1
			TF_POISON: 10
			TF_DETOXIFY: 1
			AS_ENCHANTPOISON: 5
		}
	}
}
Paladin: {
	inherit: ( "Crusader" );

	skills: {
		PA_PRESSURE: {
			MaxLevel: 5
			SM_ENDURE: 5
			CR_TRUST: 5
			CR_SHIELDCHARGE: 2
		}
		PA_SACRIFICE: {
			MaxLevel: 5
			SM_ENDURE: 1
			CR_TRUST: 5
			CR_DEVOTION: 3
		}
		PA_GOSPEL: {
			MaxLevel: 10
			CR_TRUST: 8
			AL_DP: 3
			AL_DEMONBANE: 5
		}
		PA_SHIELDCHAIN: {
			MaxLevel: 5
			CR_SHIELDBOOMERANG: 5
		}
	}
}
Champion: {
	inherit: ( "Monk" );

	skills: {
		CH_PALMSTRIKE: {
			MaxLevel: 5
			MO_IRONHAND: 7
			MO_CALLSPIRITS: 5
		}
		CH_TIGERFIST: {
			MaxLevel: 5
			MO_IRONHAND: 5
			MO_TRIPLEATTACK: 5
			MO_CALLSPIRITS: 5
			MO_COMBOFINISH: 3
		}
		CH_CHAINCRUSH: {
			MaxLevel: 10
			MO_IRONHAND: 5
			MO_CALLSPIRITS: 5
			CH_TIGERFIST: 2
		}
		CH_SOULCOLLECT: {
			MaxLevel: 1
			MO_CALLSPIRITS: 5
			MO_ABSORBSPIRITS: 1
			MO_EXPLOSIONSPIRITS: 5
		}
	}
}
Professor: {
	inherit: ( "Sage" );

	skills: {
		PF_HPCONVERSION: {
			MaxLevel: 5
			MG_SRECOVERY: 1
			SA_MAGICROD: 1
		}
		PF_SOULCHANGE: {
			MaxLevel: 1
			SA_MAGICROD: 3
			SA_SPELLBREAKER: 2
		}
		PF_SOULBURN: {
			MaxLevel: 5
			SA_CASTCANCEL: 5
			SA_MAGICROD: 3
			SA_DISPELL: 3
		}
		PF_MINDBREAKER: {
			MaxLevel: 5
			MG_SRECOVERY: 3
			PF_SOULBURN: 1
		}
		PF_MEMORIZE: {
			MaxLevel: 1
			SA_ADVANCEDBOOK: 5
			SA_FREECAST: 5
			SA_AUTOSPELL: 1
		}
		PF_FOGWALL: {
			MaxLevel: 1
			SA_DELUGE: 2
			SA_VIOLENTGALE: 2
		}
		PF_SPIDERWEB: {
			MaxLevel: 1
			SA_DRAGONOLOGY: 4
		}
		PF_DOUBLECASTING: {
			MaxLevel: 5
			SA_AUTOSPELL: 1
		}
	}
}
Stalker: {
	inherit: ( "Rogue" );

	skills: {
		ST_CHASEWALK: {
			MaxLevel: 5
			TF_HIDING: 5
			RG_TUNNELDRIVE: 3
		}
		ST_REJECTSWORD: {
			MaxLevel: 5
			RG_STRIPWEAPON: 1
		}
		ST_PRESERVE: {
			MaxLevel: 1
			RG_PLAGIARISM: 10
		}
		ST_FULLSTRIP: {
			MaxLevel: 5
			RG_STRIPWEAPON: 5
			RG_STRIPSHIELD: 5
			RG_STRIPARMOR: 5
			RG_STRIPHELM: 5
		}
	}
}
Creator: {
	inherit: ( "Alchemist" );

	skills: {
		CR_SLIMPITCHER: {
			MaxLevel: 10
			AM_POTIONPITCHER: 5
		}
		CR_FULLPROTECTION: {
			MaxLevel: 5
			AM_CP_WEAPON: 5
			AM_CP_SHIELD: 5
			AM_CP_ARMOR: 5
			AM_CP_HELM: 5
		}
		CR_ACIDDEMONSTRATION: {
			MaxLevel: 10
			AM_DEMONSTRATION: 5
			AM_ACIDTERROR: 5
		}
		CR_CULTIVATION: 2
	}
}
Clown: {
	inherit: ( "Bard" );

	skills: {
		CG_ARROWVULCAN: {
			MaxLevel: 10
			AC_SHOWER: 5
			BA_MUSICALSTRIKE: 1
		}
		CG_MOONLIT: {
			MaxLevel: 5
			AC_CONCENTRATION: 5
			BA_MUSICALLESSON: 7
		}
		CG_MARIONETTE: {
			MaxLevel: 1
			AC_CONCENTRATION: 5
			BA_MUSICALLESSON: 5
		}
		CG_LONGINGFREEDOM: {
			MaxLevel: 5
			BA_MUSICALLESSON: 10
			CG_MARIONETTE: 1
		}
		CG_HERMODE: {
			MaxLevel: 5
			AC_CONCENTRATION: 10
			BA_MUSICALLESSON: 10
		}
		CG_TAROTCARD: {
			MaxLevel: 5
			AC_CONCENTRATION: 10
			BA_DISSONANCE: 3
		}
	}
}
Gypsy: {
	inherit: ( "Dancer" );

	skills: {
		CG_ARROWVULCAN: {
			MaxLevel: 10
			AC_SHOWER: 5
			DC_THROWARROW: 1
		}
		CG_MOONLIT: {
			MaxLevel: 5
			AC_CONCENTRATION: 5
			DC_DANCINGLESSON: 7
		}
		CG_MARIONETTE: {
			MaxLevel: 1
			AC_CONCENTRATION: 5
			DC_DANCINGLESSON: 5
		}
		CG_LONGINGFREEDOM: {
			MaxLevel: 5
			DC_DANCINGLESSON: 10
			CG_MARIONETTE: 1
		}
		CG_HERMODE: {
			MaxLevel: 5
			AC_CONCENTRATION: 10
			DC_DANCINGLESSON: 10
		}
		CG_TAROTCARD: {
			MaxLevel: 5
			AC_CONCENTRATION: 10
			DC_UGLYDANCE: 3
		}
	}
}
Baby_Novice: {
	inherit: ( "Novice" );
}
Baby_Swordsman: {
	inherit: ( "Swordsman" );
}
Baby_Magician: {
	inherit: ( "Magician" );
}
Baby_Archer: {
	inherit: ( "Archer" );
}
Baby_Acolyte: {
	inherit: ( "Acolyte" );
}
Baby_Merchant: {
	inherit: ( "Merchant" );
}
Baby_Thief: {
	inherit: ( "Thief" );
}
Baby_Knight: {
	inherit: ( "Knight" );
}
Baby_Priest: {
	inherit: ( "Priest" );
}
Baby_Wizard: {
	inherit: ( "Wizard" );
}
Baby_Blacksmith: {
	inherit: ( "Blacksmith" );
}
Baby_Hunter: {
	inherit: ( "Hunter" );
}
Baby_Assassin: {
	inherit: ( "Assassin" );
}
Baby_Crusader: {
	inherit: ( "Crusader" );
}
Baby_Monk: {
	inherit: ( "Monk" );
}
Baby_Sage: {
	inherit: ( "Sage" );
}
Baby_Rogue: {
	inherit: ( "Rogue" );
}
Baby_Alchemist: {
	inherit: ( "Alchemist" );
}
Baby_Bard: {
	inherit: ( "Bard" );
}
Baby_Dancer: {
	inherit: ( "Dancer" );
}
Super_Baby: {
	inherit: ( "Super_Novice" );
}
Taekwon: {
	inherit: ( "Novice" );

	skills: {
		TK_RUN: 10
		TK_READYSTORM: {
			MaxLevel: 1
			TK_STORMKICK: 1
		}
		TK_STORMKICK: 7
		TK_READYDOWN: {
			MaxLevel: 1
			TK_DOWNKICK: 1
		}
		TK_DOWNKICK: 7
		TK_READYTURN: {
			MaxLevel: 1
			TK_TURNKICK: 1
		}
		TK_TURNKICK: 7
		TK_READYCOUNTER: {
			MaxLevel: 1
			TK_COUNTER: 1
		}
		TK_COUNTER: 7
		TK_DODGE: {
			MaxLevel: 1
			TK_JUMPKICK: 7
		}
		TK_JUMPKICK: 7
		TK_HPTIME: 10
		TK_SPTIME: 10
		TK_POWER: 5
		TK_SEVENWIND: {
			MaxLevel: 7
			TK_HPTIME: 5
			TK_SPTIME: 5
			TK_POWER: 5
		}
		TK_HIGHJUMP: 5
		TK_MISSION: {
			MaxLevel: 1
			TK_POWER: 5
		}
	}
}
Star_Gladiator: {
	inherit: ( "Taekwon" );

	skills: {
		SG_FEEL: 3
		SG_SUN_WARM: {
			MaxLevel: 3
			SG_FEEL: 1
		}
		SG_MOON_WARM: {
			MaxLevel: 3
			SG_FEEL: 2
		}
		SG_STAR_WARM: {
			MaxLevel: 3
			SG_FEEL: 3
		}
		SG_SUN_COMFORT: {
			MaxLevel: 4
			SG_FEEL: 1
		}
		SG_MOON_COMFORT: {
			MaxLevel: 4
			SG_FEEL: 2
		}
		SG_STAR_COMFORT: {
			MaxLevel: 4
			SG_FEEL: 3
		}
		SG_HATE: 3
		SG_SUN_ANGER: {
			MaxLevel: 3
			SG_HATE: 1
		}
		SG_MOON_ANGER: {
			MaxLevel: 3
			SG_HATE: 2
		}
		SG_STAR_ANGER: {
			MaxLevel: 3
			SG_HATE: 3
		}
		SG_SUN_BLESS: {
			MaxLevel: 5
			SG_FEEL: 1
			SG_HATE: 1
		}
		SG_MOON_BLESS: {
			MaxLevel: 5
			SG_FEEL: 2
			SG_HATE: 2
		}
		SG_STAR_BLESS: {
			MaxLevel: 5
			SG_FEEL: 3
			SG_HATE: 3
		}
		SG_DEVIL: 10
		SG_FRIEND: 3
		SG_KNOWLEDGE: 10
		SG_FUSION: {
			MaxLevel: 1
			SG_KNOWLEDGE: 9
		}
		LK_PARRYING: 10
		WZ_VERMILION: 10
	}
}
Soul_Linker: {
	inherit: ( "Taekwon" );

	skills: {
		SL_ALCHEMIST: 5
		SL_MONK: 5
		SL_STAR: 5
		SL_SAGE: 5
		SL_CRUSADER: 5
		SL_SUPERNOVICE: 5
		SL_KNIGHT: {
			MaxLevel: 5
			SL_CRUSADER: 1
		}
		SL_WIZARD: {
			MaxLevel: 5
			SL_SAGE: 1
		}
		SL_PRIEST: {
			MaxLevel: 5
			SL_MONK: 1
		}
		SL_BARDDANCER: 5
		SL_ROGUE: {
			MaxLevel: 5
			SL_ASSASIN: 1
		}
		SL_ASSASIN: 5
		SL_BLACKSMITH: {
			MaxLevel: 5
			SL_ALCHEMIST: 1
		}
		SL_HUNTER: {
			MaxLevel: 5
			SL_BARDDANCER: 1
		}
		SL_SOULLINKER: {
			MaxLevel: 5
			SL_STAR: 1
		}
		SL_KAIZEL: {
			MaxLevel: 7
			SL_PRIEST: 1
		}
		SL_KAAHI: {
			MaxLevel: 7
			SL_PRIEST: 1
			SL_CRUSADER: 1
		}
		SL_KAUPE: {
			MaxLevel: 3
			SL_ROGUE: 1
		}
		SL_KAITE: {
			MaxLevel: 7
			SL_WIZARD: 1
		}
		SL_KAINA: {
			MaxLevel: 7
			TK_SPTIME: 1
		}
		SL_STIN: {
			MaxLevel: 7
			SL_WIZARD: 1
		}
		SL_STUN: {
			MaxLevel: 7
			SL_WIZARD: 1
		}
		SL_SMA: {
			MaxLevel: 10
			SL_STIN: 7
			SL_STUN: 7
		}
		SL_SWOO: {
			MaxLevel: 7
			SL_PRIEST: 1
		}
		SL_SKE: {
			MaxLevel: 3
			SL_KNIGHT: 1
		}
		SL_SKA: {
			MaxLevel: 3
			SL_MONK: 1
		}
		SL_HIGH: {
			MaxLevel: 5
			SL_SUPERNOVICE: 5
		}
	}
}
Gangsi: {
	inherit: ( "Novice" );
}
Death_Knight: {
	inherit: ( "Novice" );
}
Dark_Collector: {
	inherit: ( "Novice" );
}
Rune_Knight: {
	inherit: ( "Knight" );

	skills: {
		NPC_RANGEATTACK: 8 //  Rapid Spear Boomerang 160
		NPC_HELLJUDGEMENT: 10 // Hell's Judgement 662
		NPC_DRAGONFEAR: 5	// Dragon Fear 659
		NPC_CURSEATTACK: 5	//	Cursing Attack 181
		RK_DRAGONTRAINING: 5
	}
}
Warlock: {
	inherit: ( "Wizard" );

	skills: {
		NPC_DARKSTRIKE: 10 // Soul Strike of Darkness 340
		NPC_EARTHQUAKE: 10	//	Earth Quake  653
		NPC_EVILLAND: 10	// Ebuelle Nasar 670
		NPC_WIDESIGHT: 1	// Expecto Patronum 669
	}
}
Ranger: {
	inherit: ( "Hunter" );

	skills: {
		NPC_PIERCINGATT: 5 // Piercing Arrow 158
		NPC_COMBOATTACK: 10 // Focus Fire 171
		NPC_GUIDEDATTACK: 10 // Assasinate 172
		NPC_CRITICALWOUND: 5 // Critical Wounds 673
		RA_WUGMASTERY: 1 
		RA_WUGRIDER: 3
	}
}
Arch_Bishop: {
	inherit: ( "Priest" );

	skills: {
		NPC_MENTALBREAKER:  5	// Soul Cleanser
		NPC_KEEPING: 1 // Refraction 201
		NPC_REBIRTH: 1 // Shallow Grave 208
		//NPC_MAGICMIRROR: 5 // Negrai Gorospe 671
		ZE_HOLY_RAPTURE: 5
		AB_CANTO: 3
		AB_EXPIATIO: 5
		AB_CHEAL: 3
	}
}
Mechanic: {
	inherit: ( "Blacksmith" );

	skills: {
		NPC_WEAPONBRAKER: 5 // Weapon Crush 343
		NPC_ARMORBRAKE: 5 // Armor Crush 344
		NPC_HELMBRAKE: 5 // Helm Crush 345
		NPC_SHIELDBRAKE: 5 // Shield Crush 346 
		NC_SILVERSNIPER: 5
		NC_MAGICDECOY: 5
		NC_DISJOINT: 1
	}
}
Guillotine_Cross: {
	inherit: ( "Assassin" );

	skills: {
		NPC_CHANGEPOISON: 5 // Enchant Poison Armor 166
		NPC_POISON: 5 // Toxin Injection 176
		NPC_PULSESTRIKE: 5 // Pulse Strike 661
		NPC_DARKCROSS: 10
	}
}
Rune_Knight_Trans: {
	inherit: ( "Lord_Knight", "Rune_Knight" );
}
Warlock_Trans: {
	inherit: ( "High_Wizard", "Warlock" );
}
Ranger_Trans: {
	inherit: ( "Sniper", "Ranger" );
}
Arch_Bishop_Trans: {
	inherit: ( "High_Priest", "Arch_Bishop" );
}
Mechanic_Trans: {
	inherit: ( "Whitesmith", "Mechanic" );
}
Guillotine_Cross_Trans: {
	inherit: ( "Assassin_Cross", "Guillotine_Cross" );
}
Royal_Guard: {
	inherit: ( "Crusader" );

	skills: {
		NPC_STUNATTACK: {		// Zafra's Oznerol 179
			MaxLevel: 10
			CR_SHIELDBOOMERANG: 5
		}
		NPC_GRANDDARKNESS: { //	Grand Cross of Darkness 339
			MaxLevel: 10
			CR_HOLYCROSS: 6
			CR_TRUST: 10
		}
		ZE_SPEARPULL :  5
		ZE_MANASHIELD : 5
	}
}
Sorcerer: {
	inherit: ( "Sage" );

	skills: {
		//NPC_DARKSTRIKE: 10 // Soul Strike of Darkness 340
		NPC_WIDESTONE: 5 // Curse of Medusa 666
		NPC_MAGICMIRROR: 5 // Magic Mirror 671
		ZE_POISONBOLT : 10
		ZE_GRAVITON: 5
	}
}
Minstrel: {
	inherit: ( "Bard" );

	skills: {
		NPC_SILENCEATTACK: 10	// Quiet, I'm Playing 178
		NPC_HALLUCINATION: 10 // Hallucination 207
		NPC_WIDESILENCE: 5	// Hush, Hush 663
		ZE_MURICA: 1
	}
}
Wanderer: {
	inherit: ( "Dancer" );

	skills: {
		NPC_BLINDATTACK: 10	// Stop Looking At Me 177
		NPC_HALLUCINATION: 10 // Hallucination 207
		NPC_WIDESLEEP: 5	//Hypnosis 668
		ZE_MURICA: 1
	}
}
Sura: {
	inherit: ( "Monk" );

	skills: {
		NPC_TELEKINESISATTACK: 5 // Spirit Breaker 191
		NPC_MAGICALATTACK:  10 // Demon Shock Attack 192
		PF_HPCONVERSION: 5	// HP Conversion
		ZE_QUADATTACK: 10
		ZE_GDROP: 10
	}
}
Genetic: {
	inherit: ( "Alchemist" );

	skills: {
		NPC_BLOODDRAIN: 4 			// Chemical Drain 199
		NPC_SELFDESTRUCTION: 1		// Chemical Explosion 173
		NPC_FIREATTACK: 10 			// Pyro Destruction 186
		ZE_REM_CHEM: 5
	}
}
Shadow_Chaser: {
	inherit: ( "Rogue" );

	skills: {
		NPC_SLEEPATTACK: 5	// Knock Out Blow 182
		NPC_STOP: 1	// Ensnare 342
		NPC_INVISIBLE:	5	// Invisibility 353
		NPC_WIDECURSE: 5	// Frango Rilla 677
	}
}
Royal_Guard_Trans: {
	inherit: ( "Paladin", "Royal_Guard" );
}
Sorcerer_Trans: {
	inherit: ( "Professor", "Sorcerer" );
}
Minstrel_Trans: {
	inherit: ( "Clown", "Minstrel" );
}
Wanderer_Trans: {
	inherit: ( "Gypsy", "Wanderer" );
}
Sura_Trans: {
	inherit: ( "Champion", "Sura" );
}
Genetic_Trans: {
	inherit: ( "Creator", "Genetic" );
}
Shadow_Chaser_Trans: {
	inherit: ( "Stalker", "Shadow_Chaser" );
}
Baby_Rune_Knight: {
	inherit: ( "Rune_Knight" );
}
Baby_Warlock: {
	inherit: ( "Warlock" );
}
Baby_Ranger: {
	inherit: ( "Ranger" );
}
Baby_Arch_Bishop: {
	inherit: ( "Arch_Bishop" );
}
Baby_Mechanic: {
	inherit: ( "Mechanic" );
}
Baby_Guillotine_Cross: {
	inherit: ( "Guillotine_Cross" );
}
Baby_Royal_Guard: {
	inherit: ( "Royal_Guard" );
}
Baby_Sorcerer: {
	inherit: ( "Sorcerer" );
}
Baby_Minstrel: {
	inherit: ( "Minstrel" );
}
Baby_Wanderer: {
	inherit: ( "Wanderer" );
}
Baby_Sura: {
	inherit: ( "Sura" );
}
Baby_Genetic: {
	inherit: ( "Genetic" );
}
Baby_Shadow_Chaser: {
	inherit: ( "Shadow_Chaser" );
}
Expanded_Super_Novice: {
	inherit: ( "Super_Novice" );

	skills: {
		PR_IMPOSITIO: 5
		PR_SANCTUARY: {
			MaxLevel: 10
			AL_HEAL: 1
		}
		PR_STRECOVERY: 1
		PR_GLORIA: {
			MaxLevel: 5
			PR_SANCTUARY: 7
		}
		WZ_FIREPILLAR: {
			MaxLevel: 10
			MG_FIREWALL: 1
		}
		WZ_SIGHTRASHER: {
			MaxLevel: 10
			MG_LIGHTNINGBOLT: 1
			MG_SIGHT: 1
		}
		WZ_JUPITEL: {
			MaxLevel: 10
			MG_NAPALMBEAT: 1
			MG_LIGHTNINGBOLT: 1
		}
		WZ_WATERBALL: {
			MaxLevel: 5
			MG_COLDBOLT: 1
			MG_LIGHTNINGBOLT: 1
		}
		WZ_ICEWALL: {
			MaxLevel: 10
			MG_STONECURSE: 1
			MG_FROSTDIVER: 1
		}
		WZ_FROSTNOVA: {
			MaxLevel: 10
			WZ_ICEWALL: 1
		}
		WZ_EARTHSPIKE: {
			MaxLevel: 5
			MG_STONECURSE: 1
		}
		WZ_HEAVENDRIVE: {
			MaxLevel: 5
			WZ_EARTHSPIKE: 3
		}
		WZ_QUAGMIRE: {
			MaxLevel: 5
			WZ_HEAVENDRIVE: 1
		}
		WZ_ESTIMATION: 1
		BS_HILTBINDING: 1
		BS_WEAPONRESEARCH: {
			MaxLevel: 10
			BS_HILTBINDING: 1
		}
		HT_SKIDTRAP: 5
		HT_SANDMAN: {
			MaxLevel: 5
			HT_FLASHER: 1
		}
		HT_FLASHER: {
			MaxLevel: 5
			HT_SKIDTRAP: 1
		}
		HT_FREEZINGTRAP: {
			MaxLevel: 5
			HT_FLASHER: 1
		}
		AS_ENCHANTPOISON: {
			MaxLevel: 10
			TF_POISON: 1
		}
		RG_TUNNELDRIVE: {
			MaxLevel: 5
			TF_HIDING: 1
		}
		AM_AXEMASTERY: 10
		CR_TRUST: 10
		CR_HOLYCROSS: {
			MaxLevel: 10
			CR_TRUST: 7
		}
		MO_IRONHAND: {
			MaxLevel: 10
			AL_DEMONBANE: 10
			AL_DP: 10
		}
		MO_CALLSPIRITS: {
			MaxLevel: 5
			MO_IRONHAND: 2
		}
		MO_ABSORBSPIRITS: {
			MaxLevel: 1
			MO_CALLSPIRITS: 5
		}
		HW_MAGICCRASHER: {
			MaxLevel: 1
			MG_SRECOVERY: 1
		}
		ALL_BUYING_STORE: {
			MaxLevel: 1
			MC_VENDING: 1
		}
	}
}
Expanded_Super_Baby: {
	inherit: ( "Expanded_Super_Novice" );
}
Kagerou: {
	inherit: ( "Ninja" );

	skills: {
		KO_YAMIKUMO: {
			MaxLevel: 1
			NJ_KIRIKAGE: 5
		}
		KO_RIGHT: 5
		KO_LEFT: 5
		KO_JYUMONJIKIRI: {
			MaxLevel: 5
			KO_YAMIKUMO: 1
		}
		KO_SETSUDAN: {
			MaxLevel: 5
			KO_JYUMONJIKIRI: 2
		}
		KO_BAKURETSU: {
			MaxLevel: 5
			NJ_KUNAI: 5
		}
		KO_HAPPOKUNAI: {
			MaxLevel: 5
			KO_BAKURETSU: 1
		}
		KO_MUCHANAGE: {
			MaxLevel: 10
			KO_MAKIBISHI: 3
		}
		KO_HUUMARANKA: {
			MaxLevel: 5
			NJ_HUUMA: 5
		}
		KO_MAKIBISHI: {
			MaxLevel: 5
			NJ_ZENYNAGE: 1
		}
		KO_MEIKYOUSISUI: {
			MaxLevel: 5
			NJ_NINPOU: 10
		}
		KO_ZANZOU: {
			MaxLevel: 5
			NJ_UTSUSEMI: 1
		}
		KO_KYOUGAKU: {
			MaxLevel: 5
			KO_GENWAKU: 2
		}
		KO_JYUSATSU: {
			MaxLevel: 5
			KO_KYOUGAKU: 3
		}
		KO_KAHU_ENTEN: 1
		KO_HYOUHU_HUBUKI: 1
		KO_KAZEHU_SEIRAN: 1
		KO_DOHU_KOUKAI: 1
		KO_KAIHOU: {
			MaxLevel: 1
			KO_KAHU_ENTEN: 1
			KO_HYOUHU_HUBUKI: 1
			KO_KAZEHU_SEIRAN: 1
			KO_DOHU_KOUKAI: 1
		}
		KO_ZENKAI: {
			MaxLevel: 1
			KO_KAIHOU: 1
			KO_IZAYOI: 1
		}
		KO_GENWAKU: {
			MaxLevel: 5
			NJ_UTSUSEMI: 1
		}
		KO_IZAYOI: {
			MaxLevel: 5
			NJ_NINPOU: 5
		}
		KG_KAGEHUMI: {
			MaxLevel: 5
			KO_ZANZOU: 1
		}
		KG_KYOMU: {
			MaxLevel: 5
			KG_KAGEHUMI: 2
		}
		KG_KAGEMUSYA: {
			MaxLevel: 5
			KG_KYOMU: 3
		}
	}
}
Oboro: {
	inherit: ( "Ninja" );

	skills: {
		KO_YAMIKUMO: {
			MaxLevel: 1
			NJ_KIRIKAGE: 5
		}
		KO_RIGHT: 5
		KO_LEFT: 5
		KO_JYUMONJIKIRI: {
			MaxLevel: 5
			KO_YAMIKUMO: 1
		}
		KO_SETSUDAN: {
			MaxLevel: 5
			KO_JYUMONJIKIRI: 2
		}
		KO_BAKURETSU: {
			MaxLevel: 5
			NJ_KUNAI: 5
		}
		KO_HAPPOKUNAI: {
			MaxLevel: 5
			KO_BAKURETSU: 1
		}
		KO_MUCHANAGE: {
			MaxLevel: 10
			KO_MAKIBISHI: 3
		}
		KO_HUUMARANKA: {
			MaxLevel: 5
			NJ_HUUMA: 5
		}
		KO_MAKIBISHI: {
			MaxLevel: 5
			NJ_ZENYNAGE: 1
		}
		KO_MEIKYOUSISUI: {
			MaxLevel: 5
			NJ_NINPOU: 10
		}
		KO_ZANZOU: {
			MaxLevel: 5
			NJ_UTSUSEMI: 1
		}
		KO_KYOUGAKU: {
			MaxLevel: 5
			KO_GENWAKU: 2
		}
		KO_JYUSATSU: {
			MaxLevel: 5
			KO_KYOUGAKU: 3
		}
		KO_KAHU_ENTEN: 1
		KO_HYOUHU_HUBUKI: 1
		KO_KAZEHU_SEIRAN: 1
		KO_DOHU_KOUKAI: 1
		KO_KAIHOU: {
			MaxLevel: 1
			KO_KAHU_ENTEN: 1
			KO_HYOUHU_HUBUKI: 1
			KO_KAZEHU_SEIRAN: 1
			KO_DOHU_KOUKAI: 1
		}
		KO_ZENKAI: {
			MaxLevel: 1
			KO_KAIHOU: 1
			KO_IZAYOI: 1
		}
		KO_GENWAKU: {
			MaxLevel: 5
			NJ_UTSUSEMI: 1
		}
		KO_IZAYOI: {
			MaxLevel: 5
			NJ_NINPOU: 5
		}
		OB_ZANGETSU: {
			MaxLevel: 5
			KO_GENWAKU: 1
		}
		OB_OBOROGENSOU: {
			MaxLevel: 5
			OB_AKAITSUKI: 3
		}
		OB_AKAITSUKI: {
			MaxLevel: 5
			OB_ZANGETSU: 2
		}
	}
}
Rebellion: {
	inherit: ( "Gunslinger" );

	skills: {
		RL_RICHS_COIN: {
			MaxLevel: 1
			GS_GLITTERING: 5
		}
		RL_MASS_SPIRAL: {
			MaxLevel: 5
			GS_PIERCINGSHOT: 1
		}
		RL_BANISHING_BUSTER: {
			MaxLevel: 5
			RL_S_STORM: 1
		}
		RL_B_TRAP: 5
		RL_FLICKER: {
			MaxLevel: 1
			GS_FLING: 1
		}
		RL_S_STORM: {
			MaxLevel: 5
			GS_DISARM: 1
			GS_DUST: 1
		}
		RL_E_CHAIN: {
			MaxLevel: 10
			GS_CHAINACTION: 10
		}
		RL_QD_SHOT: {
			MaxLevel: 1
			RL_E_CHAIN: 1
		}
		RL_C_MARKER: {
			MaxLevel: 1
			GS_INCREASING: 1
		}
		RL_FIREDANCE: {
			MaxLevel: 5
			RL_FALLEN_ANGEL: 1
		}
		RL_H_MINE: {
			MaxLevel: 5
			GS_SPREADATTACK: 1
		}
		RL_P_ALTER: 5
		RL_FALLEN_ANGEL: {
			MaxLevel: 5
			GS_DESPERADO: 10
		}
		RL_R_TRIP: {
			MaxLevel: 5
			RL_FIRE_RAIN: 1
		}
		RL_D_TAIL: {
			MaxLevel: 5
			RL_H_MINE: 3
			RL_C_MARKER: 1
		}
		RL_FIRE_RAIN: {
			MaxLevel: 5
			GS_GATLINGFEVER: 1
		}
		RL_HEAT_BARREL: {
			MaxLevel: 5
			RL_FIREDANCE: 2
		}
		RL_AM_BLAST: {
			MaxLevel: 5
			RL_MASS_SPIRAL: 1
		}
		RL_SLUGSHOT: {
			MaxLevel: 5
			RL_BANISHING_BUSTER: 3
		}
		RL_HAMMER_OF_GOD: {
			MaxLevel: 5
			RL_AM_BLAST: 3
		}
	}
}

Summoner: {
	skills: {
		SU_BASIC_SKILL: 1
		SU_BITE: {
			MaxLevel: 1
			SU_BASIC_SKILL: 1
		}
		SU_HIDE: {
			MaxLevel: 1
			SU_BITE: 1
		}
		SU_SCRATCH: {
			MaxLevel: 3
			SU_HIDE: 1
		}
		SU_STOOP: {
			MaxLevel: 1
			SU_SCRATCH: 3
		}
		SU_LOPE: {
			MaxLevel: 3
			SU_STOOP: 1
		}
		SU_SPRITEMABLE: {
			MaxLevel: 1
			SU_LOPE: 3
		}
		SU_POWEROFLAND: {
			MaxLevel: 1
			SU_CN_POWDERING: 3
		}
		SU_SV_STEMSPEAR: {
			MaxLevel: 5
			SU_SPRITEMABLE: 1
		}
		SU_CN_POWDERING: {
			MaxLevel: 5
			SU_CN_METEOR: 3
		}
		SU_CN_METEOR: {
			MaxLevel: 5
			SU_SV_ROOTTWIST: 3
		}
		SU_SV_ROOTTWIST: {
			MaxLevel: 5
			SU_SV_STEMSPEAR: 3
		}
		SU_POWEROFLIFE: {
			MaxLevel: 1
			SU_LUNATICCARROTBEAT: 3
		}
		SU_SCAROFTAROU: {
			MaxLevel: 5
			SU_SV_ROOTTWIST: 3
		}
		SU_PICKYPECK: {
			MaxLevel: 5
			SU_SPRITEMABLE: 1
		}
		SU_ARCLOUSEDASH: {
			MaxLevel: 5
			SU_PICKYPECK: 3
		}
		SU_LUNATICCARROTBEAT: {
			MaxLevel: 5
			SU_SCAROFTAROU: 3
		}
		SU_POWEROFSEA: {
			MaxLevel: 1
			SU_TUNAPARTY: 3
		}
		SU_TUNABELLY: {
			MaxLevel: 5
			SU_BUNCHOFSHRIMP: 3
		}
		SU_TUNAPARTY: {
			MaxLevel: 5
			SU_TUNABELLY: 3
		}
		SU_BUNCHOFSHRIMP: {
			MaxLevel: 5
			SU_FRESHSHRIMP: 3
		}
		SU_FRESHSHRIMP: {
			MaxLevel: 5
			SU_SPRITEMABLE: 1
		}	
	}

}
Padawan: {
	inherit: ( "Novice" );

	skills: { 
		SM_SWORD: 10
		SM_RECOVERY: 10
		MG_SRECOVERY: 10
		WZ_ESTIMATION: 1
		AL_HEAL: 10
		JE_FORCE:{
			MaxLevel: 10
			SM_SWORD: 5
			SM_RECOVERY: 2
			MG_SRECOVERY: 2
		}
		PA_LIGHTSABERTHROW:{
			MaxLevel: 5
			SM_SWORD: 5
		}
		PA_JUMP:{
			MaxLevel: 5
			JE_FORCE: 2
		}
		PA_PUSH:{
			MaxLevel: 8
			JE_FORCE: 2
		}
		PA_PULL:{
			MaxLevel: 5
			JE_FORCE: 5
		}
		PA_ELEMENT:{
			MaxLevel: 7
			JE_FORCE: 10
		}
		JE_FORCEBWY: 1
	}
}
Jedi: {
	inherit: ( "Padawan" );

	skills: { 
		AL_CURE: 1
		JE_DUAL: 10
		AL_RUWACH: 1
		JE_AFORCE: 10
		JE_FORCEHASTE:{
			MaxLevel: 5
			JE_AFORCE: 5
		}
		JE_SHOCK: 10
		JE_PAR:{
			MaxLevel: 10
			JE_AFORCE: 5
		}
		JE_PERSUASION:{
			MaxLevel: 5
			JE_AFORCE: 5
		}
		JE_IMPROVEDLT:{
			MaxLevel: 5
			JE_AFORCE: 10
			AL_CURE: 1
		}
		JE_SACRI:{
			MaxLevel: 5
			JE_AFORCE: 10
			AL_CURE: 1
		}
		JE_FRENZY:{
			MaxLevel: 5
			JE_AFORCE: 6
		}
		JE_CONCEN:{
			MaxLevel: 5
			JE_AFORCE: 6
		}
		JE_REGEN:5
		JE_HIDE:{
			MaxLevel: 1
			JE_AFORCE: 3
		}
		JE_LIG:{
			MaxLevel: 5
			JE_AFORCE: 5
		}
	}
}
Sith: {
	inherit: ( "Padawan" );

	skills: { 
		JE_DUAL: 10
		SI_DUPLICATE: 2
		SI_PROJECTION:{
			MaxLevel: 5
			SI_DUPLICATE: 2
		}
		SI_WAVE: 5
		JE_AFORCE: 10
		JE_PAR:{
			MaxLevel: 10
			JE_AFORCE: 5
		}
		JE_PERSUASION:{
			MaxLevel: 5
			JE_AFORCE: 1
		}
		SI_CHOKE:{
			MaxLevel: 5
			JE_AFORCE: 10
			SI_WAVE: 5
		}
		HW_SOULDRAIN:{
			MaxLevel: 10
			MG_SRECOVERY: 5
		}
		SI_SABERTHRUST:{
			MaxLevel: 5
			JE_AFORCE: 6
		}
		SI_COLDSKIN:{
			MaxLevel: 5
			JE_AFORCE: 3
		}
		JE_CONCEN:{
			MaxLevel: 5
			JE_AFORCE: 6
		}
		SI_STO:{
			MaxLevel: 5
			JE_AFORCE: 5
		}
		SI_RECALL: 5
	}
}
Necromancer: {
	inherit: ( "Priest" );
	skills: {
		NEC_SUMMON1: 5
		NEC_SUMMON2: 5
		NEC_SUMMON3: 5
		NEC_RECALL: 1
		NEC_SUMMON4: 5
		NEC_SUMMON5: 5
		NEC_SUMMON6: 5
		NEC_CURSEDWATER: 1
		NEC_THRILLER: 5
		NEC_DEADLYPUPPET: 5
		NEC_UBOMB: 10
		NEC_CPOSSESSION: 6
		NEC_DHOLE: 10
		NEC_TWISTSOUL: 5
		NEC_DEMONTOUCH: 10
		NEC_CSHADOWS: 5
		ZE_CHANGETARGET: 1
	}
}
Kage: {
	inherit: ( "Ninja" );
	skills: {
		KAG_RASENGAN: 5  //708
		KAG_BUNSHIN: 10
		KAG_TAJUUBUNSHIN: 10
		KAG_KUCHIYOSE: 5
		KAG_GYAKU: 1
		NPC_POWERUP: 5	//349 Sage Mode
		NPC_AGIUP: 5	//350 Jinton No Jutsu
		//NPC_GUIDEDATTACK: 5 //172 Muon Satsujin Jutsu 
		KAG_MUON: 5
		NPC_DEFENDER: 10 //Fuujutsu Kyuuin exists forgot id
		//NPC_PULSESTRIKE: 5 // 661 Shinra Tensei	
		KAG_SHINRA: 5
		KAG_NARAKUMI: 1
		KAG_SENSATSU_SUISHOU: 10
		ZE_CHANGETARGET: 1
	}
}

Star_Emperor: {
	inherit: ( "Star_Gladiator" );
}
Soul_Reaper: {
	inherit: ( "Star_Gladiator" );
}
Imperial_Guard: {
	inherit: ( "Paladin", "Royal_Guard" );
}
Elemental_Master: {
	inherit: ( "Professor", "Sorcerer" );
}
Troubadour: {
	inherit: ( "Clown", "Minstrel" );
}
Trouvere: {
	inherit: ( "Gypsy", "Wanderer" );
}
Inquisitor: {
	inherit: ( "Champion", "Sura" );
}
Biolo: {
	inherit: ( "Creator", "Genetic" );
}
Abyss_Chaser: {
	inherit: ( "Stalker", "Shadow_Chaser" );
}
Dragon_Knight: {
	inherit: ( "Lord_Knight", "Rune_Knight" );
}
Arch_Mage: {
	inherit: ( "High_Wizard", "Warlock" );
}
Windhawk: {
	inherit: ( "Sniper", "Ranger" );
}
Cardinal: {
	inherit: ( "High_Priest", "Arch_Bishop" );
}
Meister: {
	inherit: ( "Whitesmith", "Mechanic" );
}
Shadow_Cross: {
	inherit: ( "Assassin_Cross", "Guillotine_Cross" );
}
Baby_Padawan: {
	inherit: ( "Padawan" );
}
Baby_Jedi: {
	inherit: ( "Jedi" );
}
Baby_Sith: {
	inherit: ( "Sith" );
}
Baby_Star_Emperor: {
	inherit: ( "Star_Gladiator" );
}
Baby_Soul_Reaper: {
	inherit: ( "Star_Gladiator" );
}
Baby_Imperial_Guard: {
	inherit: ( "Paladin", "Royal_Guard" );
}
Baby_Elemental_Master: {
	inherit: ( "Professor", "Sorcerer" );
}
Baby_Troubadour: {
	inherit: ( "Clown", "Minstrel" );
}
Baby_Trouvere: {
	inherit: ( "Gypsy", "Wanderer" );
}
Baby_Inquisitor: {
	inherit: ( "Champion", "Sura" );
}
Baby_Biolo: {
	inherit: ( "Creator", "Genetic" );
}
Baby_Abyss_Chaser: {
	inherit: ( "Stalker", "Shadow_Chaser" );
}
Baby_Dragon_Knight: {
	inherit: ( "Lord_Knight", "Rune_Knight" );
}
Baby_Arch_Mage: {
	inherit: ( "High_Wizard", "Warlock" );
}
Baby_Windhawk: {
	inherit: ( "Sniper", "Ranger" );
}
Baby_Cardinal: {
	inherit: ( "High_Priest", "Arch_Bishop" );
}
Baby_Meister: {
	inherit: ( "Whitesmith", "Mechanic" );
}
Baby_Shadow_Cross: {
	inherit: ( "Assassin_Cross", "Guillotine_Cross" );
}
Baby_Lord_Knight: {
	inherit: ( "Lord_Knight" );
}
Baby_High_Priest: {
	inherit: ( "High_Priest" );
}
Baby_High_Wizard: {
	inherit: ( "High_Wizard" );
}
Baby_Whitesmith: {
	inherit: ( "Whitesmith" );
}
Baby_Sniper: {
	inherit: ( "Sniper" );
}
Baby_Assassin_Cross: {
	inherit: ( "Assassin_Cross" );
}
Baby_Paladin: {
	inherit: ( "Paladin" );
}
Baby_Champion: {
	inherit: ( "Champion" );
}
Baby_Professor: {
	inherit: ( "Professor" );
}
Baby_Stalker: {
	inherit: ( "Stalker" );
}
Baby_Creator: {
	inherit: ( "Creator" );
}
Baby_Clown: {
	inherit: ( "Clown" );
}
Baby_Gypsy: {
	inherit: ( "Gypsy" );
}
