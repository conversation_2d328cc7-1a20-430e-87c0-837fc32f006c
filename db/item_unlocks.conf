item_unlocks_db: (
/**************************************************************************
 ************* Entry structure ********************************************
 **************************************************************************
{
	// ================ Mandatory fields ==============================
	AegisName: "Aegis_Name"       (string)
	// ================ Optional fields ===============================
	Amount:                       (int, defaults to 1)
	Rate:                         (int, defaults to 10000)
	                              1 = 0.01%, 10 = 0.1%, 100 = 1%, 1000 = 10%, 10000 = 100%
	Script: <"">                  Script effect of the when item unlocked.
},
*/
{
	AegisName: "Poring_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "LUK +1"
},
{
	AegisName: "Fabre_Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "VIT +1"
},
{
	AegisName: "Pupa_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Drops_Card"
	Script: <" bonus bDex,1; ">
	Rate: 3
	Info: "DEX +1"
},
{
	AegisName: "Poring__Card"
	Script: <" bonus2 bAddEle,Ele_Dark,1; ">
	Rate: 3
	Info: "Dark Property damage +1%"
},
{
	AegisName: "Lunatic_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "LUK +1"
},
{
	AegisName: "Pecopeco_Egg_Card"
	Script: <" bonus2 bAddRace,RC_Formless,1; ">
	Rate: 3
	Info: "Formless monster damage +1%"
},
{
	AegisName: "Picky_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "ATK +3"
},
{
	AegisName: "Chonchon_Card"
	Script: <" bonus bFlee,1; ">
	Rate: 3
	Info: "Flee +1"
},
{
	AegisName: "Wilow_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "MATK +3"
},
{
	AegisName: "Picky__Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "VIT +1"
},
{
	AegisName: "Thief_Bug_Egg_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Andre_Egg_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Roda_Frog_Card"
	Script: <" bonus2 bSkillAtk,MO_FINGEROFFENSIVE,1; ">
	Rate: 3
	Info: "Finger Offensive damage +1"
},
{
	AegisName: "Condor_Card"
	Script: <" bonus bFlee,1; ">
	Rate: 3
	Info: "FLEE +1"
},
{
	AegisName: "Thief_Bug_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "AGI +1"
},
{
	AegisName: "Savage_Babe_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Andre_Larva_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "MATK +3"
},
{
	AegisName: "Hornet_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "ATK +3"
},
{
	AegisName: "Farmiliar_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Rocker_Card"
	Script: <" bonus bHit,1; ">
	Rate: 3
	Info: "Hit +1"
},
{
	AegisName: "Spore_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Desert_Wolf_Babe_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Plankton_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Skeleton_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Thief_Bug_Female_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "AGI +1"
},
{
	AegisName: "Kukre_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "AGI +1"
},
{
	AegisName: "Tarou_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Wolf_Card"
	Script: <" bonus bCritical,1; ">
	Rate: 3
	Info: "Crit +1"
},
{
	AegisName: "Mandragora_Card"
	Script: <" bonus2 bAddEle,Ele_Wind,1; ">
	Rate: 3
	Info: "Wind Property damage +1%"
},
{
	AegisName: "Pecopeco_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Ambernite_Card"
	Script: <" bonus2 bSubRace,RC_Boss,1; ">
	Rate: 3
	Info: "Reduce damage from Boss by 1%"
},
{
	AegisName: "Poporing_Card"
	Script: <" bonus2 bSubSize,Size_Small,1; ">
	Rate: 3
	Info: "Reduce damage from Small monsters by 1%"
},
{
	AegisName: "Worm_Tail_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Hydra_Card"
	Script: <" bonus bMatk,2; bonus bBaseAtk,2; ">
	Rate: 3
	Info: "Atk, Matk +3"
},
{
	AegisName: "Muka_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Snake_Card"
	Script: <" bonus bBaseAtk,2; ">
	Rate: 3
	Info: "Atk +2"
},
{
	AegisName: "Zombie_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Stainer_Card"
	Script: <" bonus2 bSubRace,RC_Boss,1; ">
	Rate: 3
	Info: "Reduce damage from Boss by 1%"
},
{
	AegisName: "Creamy_Card"
	Script: <" skill AL_TELEPORT,1; ">
	Rate: 3
	Info: "Enable use of Level 1 Teleport"
},
{
	AegisName: "Coco_Card"
	Script: <" bonus bDef,1; ">
	Rate: 3
	Info: "Def +1"
},
{
	AegisName: "Steel_Chonchon_Card"
	Script: <" bonus2 bSubEle,Ele_Wind,1; ">
	Rate: 3
	Info: "Resistance to Wind Property +1%"
},
{
	AegisName: "Andre_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Smokie_Card"
	Script: <" bonus2 bSubSize,Size_Small,1; ">
	Rate: 3
	Info: "Reduce damage from Small monsters by 1%"
},
{
	AegisName: "Horn_Card"
	Script: <" bonus bLongAtkDef,1; ">
	Rate: 3
	Info: "Reduce Long Range attacks by 1%"
},
{
	AegisName: "Martin_Card"
	Script: <" bonus2 bSubSize,Size_Small,1; ">
	Rate: 3
	Info: "Reduce damage from Small monsters by 1%"
},
{
	AegisName: "Ghostring_Card"
	Script: <" bonus2 bAddEle,Ele_Ghost,7; ">
	Rate: 3
	Info: "Ghost Property damage +7%"
},
{
	AegisName: "Poison_Spore_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Vadon_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Thief_Bug_Male_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Yoyo_Card"
	Script: <" bonus bFlee2,1; ">
	Rate: 3
	Info: "Perfect Dodge +1"
},
{
	AegisName: "Elder_Wilow_Card"
	Script: <" bonus2 bSkillAtk,MG_FIREBOLT,1; ">
	Rate: 3
	Info: "Fire Bolt damage +1%"
},
{
	AegisName: "Vitata_Card"
	Script: <" bonus bHealPower,5; ">
	Rate: 3
	Info: "Heal Effectiveness + 5%"
},
{
	AegisName: "Angeling_Card"
	Script: <" bonus2 bAddEle,Ele_Holy,7; ">
	Rate: 3
	Info: "Holy Property damage +7%"
},
{
	AegisName: "Marina_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Dustiness_Card"
	Script: <" bonus2 bAddEle,Ele_Wind,1; ">
	Rate: 3
	Info: "Wind Property damage +1%"
},
{
	AegisName: "Metaller_Card"
	Script: <" bonus bHit,1; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Thara_Frog_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Soldier_Andre_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Goblin_Card"
	Script: <" bonus2 bAddRace,RC_Brute,1; ">
	Rate: 3
	Info: "Brute monster damage +1%"
},
{
	AegisName: "Cornutus_Card"
	Script: <" bonus bDef,1; ">
	Rate: 3
	Info: "Def +1"
},
{
	AegisName: "Anacondaq_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Caramel_Card"
	Script: <" bonus2 bAddRace,RC_Insect,1; ">
	Rate: 3
	Info: "Insect monster damage +1%"
},
{
	AegisName: "Zerom_Card"
	Script: <" bonus bDex,1; ">
	Rate: 3
	Info: "Dex +1"
},
{
	AegisName: "Kaho_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Orc_Warrior_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Megalodon_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Scorpion_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Drainliar_Card"
	Script: <" bonus2 bAddEle,Ele_Water,1; ">
	Rate: 3
	Info: "Water Property damage +1%"
},
{
	AegisName: "Eggyra_Card"
	Script: <" bonus bMaxSP,100; ">
	Rate: 3
	Info: "SP +100"
},
{
	AegisName: "Orc_Zombie_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Golem_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Pirate_Skel_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "BigFoot_Card"
	Script: <" bonus2 bAddRace,RC_Insect,1; ">
	Rate: 3
	Info: "Insect monster damage +1%"
},
{
	AegisName: "Argos_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Magnolia_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Phen_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Savage_Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "Vit +1"
},
{
	AegisName: "Mantis_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Flora_Card"
	Script: <" bonus2 bAddRace,RC_Fish,1; ">
	Rate: 3
	Info: "Fish monster damage +1%"
},
{
	AegisName: "Hode_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Desert_Wolf_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Rafflesia_Card"
	Script: <" bonus2 bAddRace,RC_Fish,1; ">
	Rate: 3
	Info: "Fish monster damage +1%"
},
{
	AegisName: "Marine_Sphere_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Orc_Skeleton_Card"
	Script: <" bonus2 bAddEle,Ele_Holy,1; ">
	Rate: 3
	Info: "Holy Property damage +1%"
},
{
	AegisName: "Soldier_Skeleton_Card"
	Script: <" bonus bCritical,1; ">
	Rate: 3
	Info: "Critical Rate +1%"
},
{
	AegisName: "Giearth_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Frilldora_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Sword_Fish_Card"
	Script: <" bonus2 bAddEle,Ele_Water,1; ">
	Rate: 3
	Info: "Water Property damage +1%"
},
{
	AegisName: "Munak_Card"
	Script: <" bonus2 bAddEle,Ele_Earh,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Kobold_Card"
	Script: <" bonus bBaseAtk,2; bonus bCritical,2; ">
	Rate: 3
	Info: "Atk +2, Crit +2"
},
{
	AegisName: "Skel_Worker_Card"
	Script: <" bonus2 bAddSize,Size_Small,1; ">
	Rate: 3
	Info: "Small monster damage +1%"
},
{
	AegisName: "Obeaune_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Archer_Skeleton_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Marse_Card"
	Script: <" bonus2 bAddEle,Ele_Water,1; ">
	Rate: 3
	Info: "Water Property damage +1%"
},
{
	AegisName: "Zenorc_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Matyr_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Dokebi_Card"
	Script: <" bonus2 bAddEle,Ele_Wind,1; ">
	Rate: 3
	Info: "Wind Property damage +1%"
},
{
	AegisName: "Pasana_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Sohee_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Sand_Man_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Whisper_Card"
	Script: <" bonus2 bAddEle,Ele_Ghost,1; ">
	Rate: 3
	Info: "Ghost Property damage +1%"
},
{
	AegisName: "Horong_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Requiem_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Marc_Card"
	Script: <" bonus2 bAddEle,Ele_Water,1; ">
	Rate: 3
	Info: "Water Property damage +1%"
},
{
	AegisName: "Mummy_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Verit_Card"
	Script: <" bonus bMaxHP,50; bonus bMaxSP,50; ">
	Rate: 3
	Info: "HP & SP +50"
},
{
	AegisName: "Myst_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Jakk_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Ghoul_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Strouf_Card"
	Script: <" bonus2 bAddRace,RC_Demon,1; ">
	Rate: 3
	Info: "Demon monster damage +1%"
},
{
	AegisName: "Marduk_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Marionette_Card"
	Script: <" bonus2 bAddEle,Ele_Ghost,1; ">
	Rate: 3
	Info: "Ghost Property damage +1%"
},
{
	AegisName: "Argiope_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Hunter_Fly_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Isis_Card"
	Script: <" bonus2 bAddEle,Ele_Dark,1; ">
	Rate: 3
	Info: "Dark Property damage +1%"
},
{
	AegisName: "Side_Winder_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Petit_Card"
	Script: <" bonus2 bAddRace,RC_Dragon,1; ">
	Rate: 3
	Info: "Dragon monster damage +1%"
},
{
	AegisName: "Bathory_Card"
	Script: <" bonus2 bAddEle,Ele_Dark,1; ">
	Rate: 3
	Info: "Dark Property damage +1%"
},
{
	AegisName: "Petit__Card"
	Script: <" bonus2 bAddRace,RC_Dragon,1; ">
	Rate: 3
	Info: "Dragon monster damage +1%"
},
{
	AegisName: "Phreeoni_Card"
	Script: <" bonus bHit,1; ">
	Rate: 3
	Info: "Hit +20"
},
{
	AegisName: "Deviruchi_Card"
	Script: <" bonus2 bResEff,Eff_Blind,10000; ">
	Rate: 3
	Info: "Gain immunity to Blind status"
},
{
	AegisName: "Eddga_Card"
	Script: <" bonus2 bSubEle,Ele_Fire,7; ">
	Rate: 3
	Info: "Enable effect of Endure skill"
},
{
	AegisName: "Medusa_Card"
	Script: <" bonus2 bResEff,Eff_Stone,10000; ">
	Rate: 3
	Info: "Gain immunity to Curse status"
},
{
	AegisName: "Deviace_Card"
	Script: <" bonus2 bAddRace,RC_Brute,1; ">
	Rate: 3
	Info: "Brute monster damage +1%"
},
{
	AegisName: "Minorous_Card"
	Script: <" bonus2 bAddSize,Size_Large,2; ">
	Rate: 3
	Info: "Large monster damage +2%"
},
{
	AegisName: "Nightmare_Card"
	Script: <" bonus2 bResEff,Eff_Sleep,10000; ">
	Rate: 3
	Info: "Gain immunity to Sleep status"
},
{
	AegisName: "Golden_Bug_Card"
	Script: <" bonus bNoMagicDamage,5; ">
	Rate: 3
	Info: "Nullify 5% magic spells"
},
{
	AegisName: "Baphomet__Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Scorpion_King_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Moonlight_Flower_Card"
	Script: <" bonus bMaxHPrate,5; ">
	Rate: 3
	Info: "Maximum HP +5%"
},
{
	AegisName: "Mistress_Card"
	Script: <" bonus bNoGemStone,0; ">
	Rate: 3
	Info: "Nullify Gemstone requirement of certain spells"
},
{
	AegisName: "Daydric_Card"
	Script: <" bonus2 bAddEle,Ele_Neutral,2; ">
	Rate: 3
	Info: "Neutral Property damage +2%"
},
{
	AegisName: "Dracula_Card"
	Script: <" bonus2 bSpDrainRate,100,5; ">
	Rate: 3
	Info: "Enable 10% chance of gaining 5% of enemy SP with each attack"
},
{
	AegisName: "Orc_Load_Card"
	Script: <" bonus bShortWeaponDamageReturn,5; ">
	Rate: 3
	Info: "Physical reflect damage +5%"
},
{
	AegisName: "Khalitzburg_Card"
	Script: <" bonus2 bAddRace,RC_Demon,2; ">
	Rate: 3
	Info: "Demon monster damage +2%"
},
{
	AegisName: "Drake_Card"
	Script: <" bonus2 bAddRace,RC_DemiPlayer,3; ">
	Rate: 3
	Info: "Demihuman monster damage +3%"
},
{
	AegisName: "Anubis_Card"
	Script: <" bonus2 bSubRace,RC_Angel,3; ">
	Rate: 3
	Info: "Angel monster damage +3%"
},
{
	AegisName: "Joker_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Knight_Of_Abyss_Card"
	Script: <" bonus2 bAddRace,RC_Boss,4; ">
	Rate: 3
	Info: "Boss monster damage +4%"
},
{
	AegisName: "Evil_Druid_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Doppelganger_Card"
	Script: <" bonus bAgi,5; ">
	Rate: 3
	Info: "Agi +5"
},
{
	AegisName: "Orc_Hero_Card"
	Script: <" bonus bVit,3; ">
	Rate: 3
	Info: "Vit +3"
},
{
	AegisName: "Osiris_Card"
	Script: <" bonus bInt,3; ">
	Rate: 3
	Info: "Int +3"
},
{
	AegisName: "Berzebub_Card"
	Script: <" bonus bMaxHPrate,5; ">
	Rate: 3
	Info: "Maximum HP +5%"
},
{
	AegisName: "Maya_Card"
	Script: <" bonus bMagicDamageReturn,7; ">
	Rate: 3
	Info: "Magic spells reflect +7%"
},
{
	AegisName: "Baphomet_Card"
	Script: <" bonus bSplashRange,2; ">
	Rate: 3
	Info: "Splash attack +2 cell"
},
{
	AegisName: "Pharaoh_Card"
	Script: <" bonus bUseSPrate,-10; ">
	Rate: 3
	Info: "Reduce SP Consumption of skills by 10%"
},
{
	AegisName: "Gargoyle_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Goat_Card"
	Script: <" bonus bMdef,1; ">
	Rate: 3
	Info: "MDEF +1"
},
{
	AegisName: "Gajomart_Card"
	Script: <" bonus bMdef,1; ">
	Rate: 3
	Info: "MDEF +1"
},
{
	AegisName: "Galapago_Card"
	Script: <" bonus bMdef,1; ">
	Rate: 3
	Info: "MDEF +1"
},
{
	AegisName: "Crab_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Rice_Cake_Boy_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Goblin_Leader_Card"
	Script: <" bonus bAtkRate,1; ">
	Rate: 3
	Info: "Atk +1%"
},
{
	AegisName: "Steam_Goblin_Card"
	Script: <" bonus bCritical,1; ">
	Rate: 3
	Info: "Crit +1"
},
{
	AegisName: "Goblin_Archer_Card"
	Script: <" bonus bCritical,1; ">
	Rate: 3
	Info: "Crit +1"
},
{
	AegisName: "Flying_Deleter_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Nine_Tail_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Antique_Firelock_Card"
	Script: <" bonus bStr,1; ">
	Rate: 3
	Info: "Str +1"
},
{
	AegisName: "Grand_Peco_Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "Vit +1"
},
{
	AegisName: "Grizzly_Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "Vit +1"
},
{
	AegisName: "Gryphon_Card"
	Script: <" bonus bFlee,2; ">
	Rate: 3
	Info: "Flee +2"
},
{
	AegisName: "Gullinbursti_Card"
	Script: <" bonus2 bAddRace,RC_Fish,1; ">
	Rate: 3
	Info: "Fish monster damage +1%"
},
{
	AegisName: "Gig_Card"
	Script: <" bonus bMaxSP,100; ">
	Rate: 3
	Info: "SP +100"
},
{
	AegisName: "Nightmare_Terror_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Neraid_Card"
	Script: <" bonus bMaxSP,100; ">
	Rate: 3
	Info: "SP +100"
},
{
	AegisName: "Dark_Lord_Card"
	Script: <" bonus2 bSkillAtk,WZ_METEOR,5; ">
	Rate: 3
	Info: "Meteor Storm damage +5%"
},
{
	AegisName: "Dark_Illusion_Card"
	Script: <" bonus bMaxHPrate,3; bonus bMaxSPrate,3; ">
	Rate: 3
	Info: "Maximum HP & SP +3%"
},
{
	AegisName: "Dark_Frame_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Dark_Priest_Card"
	Script: <" bonus2 bSpDrainRate,100,5; ">
	Rate: 3
	Info: "Enable 10% chance of gaining 5% of enemy SP with each attack"
},
{
	AegisName: "The_Paper_Card"
	Script: <" bonus bCritAtkRate,1; ">
	Rate: 3
	Info: "Critical damage +1%"
},
{
	AegisName: "Demon_Pungus_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Deviling_Card"
	Script: <" bonus bAtkRate,3; bonus bMatkRate,3; ">
	Rate: 3
	Info: "Atk & Matk +3%"
},
{
	AegisName: "Poison_Toad_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Dullahan_Card"
	Script: <" bonus bCritAtkRate,1; ">
	Rate: 3
	Info: "Critical damage +1%"
},
{
	AegisName: "Dryad_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Dragon_Tail_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Dragon_Fly_Card"
	Script: <" bonus bFlee,2; ">
	Rate: 3
	Info: "Flee +2"
},
{
	AegisName: "Driller_Card"
	Script: <" bonus bFlee,1; ">
	Rate: 3
	Info: "Flee +1"
},
{
	AegisName: "Disguise_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Diabolic_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Vagabond_Wolf_Card"
	Script: <" bonus bBaseAtk,3; bonus bMatk,3; ">
	Rate: 3
	Info: "Atk & Matk +3"
},
{
	AegisName: "Lava_Golem_Card"
	Script: <" bonus bDef,1; ">
	Rate: 3
	Info: "Def +1"
},
{
	AegisName: "Rideword_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Raggler_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Raydric_Archer_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Leib_Olmai_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Wraith_Dead_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Wraith_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Loli_Ruri_Card"
	Script: <" bonus bHealPower,5; ">
	Rate: 3
	Info: "Heal Effectiveness + 5%"
},
{
	AegisName: "Rotar_Zairo_Card"
	Script: <" bonus bCritAtkRate,1; ">
	Rate: 3
	Info: "Critical damage +1%"
},
{
	AegisName: "Lude_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Rybio_Card"
	Script: <" bonus bDex,1; ">
	Rate: 3
	Info: "Dex +1"
},
{
	AegisName: "Leaf_Cat_Card"
	Script: <" bonus2 bAddEle,Ele_Water,1; ">
	Rate: 3
	Info: "Water Property damage +1%"
},
{
	AegisName: "Marin_Card"
	Script: <" bonus2 bAddEle,Ele_Water,1; ">
	Rate: 3
	Info: "Water Property damage +1%"
},
{
	AegisName: "Mastering_Card"
	Script: <" bonus bLuk,2; ">
	Rate: 3
	Info: "Luk +2"
},
{
	AegisName: "Maya_Puple_Card"
	Script: <" bonus bInt,3; ">
	Rate: 3
	Info: "Int +3"
},
{
	AegisName: "Merman_Card"
	Script: <" bonus2 bAddRace,RC_Fish,1; ">
	Rate: 3
	Info: "Fish monster damage +1%"
},
{
	AegisName: "Megalith_Card"
	Script: <" bonus bMdef,1; ">
	Rate: 3
	Info: "MDEF +1"
},
{
	AegisName: "Majoruros_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Civil_Servant_Card"
	Script: <" bonus2 bAddEle,Ele_Ghost,1; ">
	Rate: 3
	Info: "Ghost Property damage +1%"
},
{
	AegisName: "Mutant_Dragon_Card"
	Script: <" bonus bStr,1; ">
	Rate: 3
	Info: "Str +1"
},
{
	AegisName: "Mini_Demon_Card"
	Script: <" bonus2 bAddRace,RC_Brute,1; ">
	Rate: 3
	Info: "Brute monster damage +1%"
},
{
	AegisName: "Mimic_Card"
	Script: <" bonus bHit,1; ">
	Rate: 3
	Info: "Hit +1"
},
{
	AegisName: "Mystcase_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Mysteltainn_Card"
	Script: <" bonus2 bAddSize,Size_Small,3; ">
	Rate: 3
	Info: "Small monster damage +3%"
},
{
	AegisName: "Miyabi_Ningyo_Card"
	Script: <" bonus bMaxSP,100; ">
	Rate: 3
	Info: "SP +100"
},
{
	AegisName: "Violy_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Wander_Man_Card"
	Script: <" bonus bFlee,1; ">
	Rate: 3
	Info: "Flee +1"
},
{
	AegisName: "Vocal_Card"
	Script: <" bonus bFlee,3; ">
	Rate: 3
	Info: "Flee +3"
},
{
	AegisName: "Bon_Gun_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Brilight_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Bloody_Murderer_Card"
	Script: <" bonus bCritAtkRate,1; ">
	Rate: 3
	Info: "Critical damage +1%"
},
{
	AegisName: "Blazzer_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Sasquatch_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Live_Peach_Tree_Card"
	Script: <" bonus bHealPower,5; ">
	Rate: 3
	Info: "Heal Effectiveness + 5%"
},
{
	AegisName: "Succubus_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Sageworm_Card"
	Script: <" bonus2 bAddSize,Size_Small,1; ">
	Rate: 3
	Info: "Small monster damage +1%"
},
{
	AegisName: "Solider_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Skeleton_General_Card"
	Script: <" bonus2 bAddRace,RC_Insect,1; ">
	Rate: 3
	Info: "Insect monster damage +1%"
},
{
	AegisName: "Skel_Prisoner_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Stalactic_Golem_Card"
	Script: <" bonus bDef,1; ">
	Rate: 3
	Info: "Def +1"
},
{
	AegisName: "Stem_Worm_Card"
	Script: <" bonus2 bAddRace,RC_Brute,1; ">
	Rate: 3
	Info: "Brute monster damage +1%"
},
{
	AegisName: "Stone_Shooter_Card"
	Script: <" bonus bHit,1; bonus bBaseAtk,1; ">
	Rate: 3
	Info: "Hit & Atk +1"
},
{
	AegisName: "Sting_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Spring_Rabbit_Card"
	Script: <" bonus2 bAddRace,RC_Brute,1; ">
	Rate: 3
	Info: "Brute monster damage +1%"
},
{
	AegisName: "Sleeper_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "C_Tower_Manager_Card"
	Script: <" bonus bInt,1; ">
	Rate: 3
	Info: "Int +1"
},
{
	AegisName: "Shinobi_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Increase_Soil_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Wild_Ginseng_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Baby_Leopard_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Anolian_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Cookie_XMAS_Card"
	Script: <" bonus2 bSubRace,RC_Angel,1; ">
	Rate: 3
	Info: "Angel monster damage +1%"
},
{
	AegisName: "Amon_Ra_Card"
	Script: <" bonus2 bAddSize,Size_Large,4; ">
	Rate: 3
	Info: "Large monster damage +4%"
},
{
	AegisName: "Owl_Duke_Card"
	Script: <" bonus bStr,1; ">
	Rate: 3
	Info: "Str +1"
},
{
	AegisName: "Owl_Baron_Card"
	Script: <" bonus bInt,1; ">
	Rate: 3
	Info: "Int +1"
},
{
	AegisName: "Iron_Fist_Card"
	Script: <" bonus2 bAddRace,RC_Formless,1; ">
	Rate: 3
	Info: "Formless monster damage +1%"
},
{
	AegisName: "Arclouse_Card"
	Script: <" bonus2 bAddSize,Size_Medium,1; ">
	Rate: 3
	Info: "Medium monster damage +1%"
},
{
	AegisName: "Archangeling_Card"
	Script: <" bonus bMaxHPrate,3; ">
	Rate: 3
	Info: "Maximum HP +3%"
},
{
	AegisName: "Apocalips_Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "Vit +1"
},
{
	AegisName: "Antonio_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Alarm_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Am_Mut_Card"
	Script: <" bonus2 bAddRace,RC_DemiPlayer,1; ">
	Rate: 3
	Info: "Demihuman monster damage +1%"
},
{
	AegisName: "Assulter_Card"
	Script: <" bonus bCritAtkRate,1; ">
	Rate: 3
	Info: "Critical damage +1%"
},
{
	AegisName: "Aster_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Ancient_Mummy_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Ancient_Worm_Card"
	Script: <" bonus2 bAddRace,RC_Demon,1; ">
	Rate: 3
	Info: "Demon monster damage +1%"
},
{
	AegisName: "Executioner_Card"
	Script: <" bonus2 bAddSize,Size_Large,2; ">
	Rate: 3
	Info: "Large monster damage +2%"
},
{
	AegisName: "Elder_Card"
	Script: <" bonus2 bAddEle,Ele_Neutral,1; ">
	Rate: 3
	Info: "Neutral Property damage +1%"
},
{
	AegisName: "Alligator_Card"
	Script: <" bonus bLongAtkDef,1; ">
	Rate: 3
	Info: "Long Range defense +1%"
},
{
	AegisName: "Alice_Card"
	Script: <" bonus2 bAddSize,Size_Large,1; ">
	Rate: 3
	Info: "Large monster damage +1%"
},
{
	AegisName: "Tirfing_Card"
	Script: <" bonus2 bAddSize,Size_Medium,1; ">
	Rate: 3
	Info: "Medium monster damage +1%"
},
{
	AegisName: "Orc_Lady_Card"
	Script: <" bonus bBaseAtk,2; bonus bMatk,2; ">
	Rate: 3
	Info: "Atk & Matk +2"
},
{
	AegisName: "Orc_Archer_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Wild_Rose_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Wicked_Nymph_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Wooden_Golem_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Wootan_Shooter_Card"
	Script: <" bonus bDex,1; ">
	Rate: 3
	Info: "Dex +1"
},
{
	AegisName: "Wootan_Fighter_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Evil_Cloud_Hermit_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Incant_Samurai_Card"
	Script: <" bonus bStr,3; ">
	Rate: 3
	Info: "Str +3"
},
{
	AegisName: "Wind_Ghost_Card"
	Script: <" bonus2 bSkillAtk,WZ_JUPITEL,1; ">
	Rate: 3
	Info: "Jupitel Thunder damage +1%"
},
{
	AegisName: "Li_Me_Mang_Ryang_Card"
	Script: <" bonus2 bSubRace,RC_Angel,1; ">
	Rate: 3
	Info: "Angel monster damage +1%"
},
{
	AegisName: "Eclipse_Card"
	Script: <" bonus bVit,2; ">
	Rate: 3
	Info: "Vit +2"
},
{
	AegisName: "Explosion_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Injustice_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Incubus_Card"
	Script: <" bonus bInt,1; ">
	Rate: 3
	Info: "Int +1"
},
{
	AegisName: "Giant_Spider_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Giant_Honet_Card"
	Script: <" bonus2 bAddEle,Ele_Wind,1; ">
	Rate: 3
	Info: "Wind Property damage +1%"
},
{
	AegisName: "Dancing_Dragon_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Shellfish_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Zombie_Master_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Zombie_Prisoner_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Lord_Of_Death_Card"
	Script: <" bonus3 bAutoSpell,PR_LEXAETERNA,1,50; ">
	Rate: 3
	Info: "Enable autocasting Level 1 Lex Aeterna when attacking"
},
{
	AegisName: "Zherlthsh_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Gibbet_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Deleter_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Geographer_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Zipper_Bear_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Tengu_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Greatest_General_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Chepet_Card"
	Script: <" bonus bHealPower,5; ">
	Rate: 3
	Info: "Heal Effectiveness + 5%"
},
{
	AegisName: "Choco_Card"
	Script: <" bonus bFlee2,1; ">
	Rate: 3
	Info: "Perfect Dodge +1"
},
{
	AegisName: "Karakasa_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Kapha_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Carat_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Caterpillar_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Cat_O_Nine_Tail_Card"
	Script: <" bonus bMagicDamageReturn,5; ">
	Rate: 3
	Info: "Magic spells reflect +5%"
},
{
	AegisName: "Kobold_Leader_Card"
	Script: <" bonus2 bSubRace,RC_Boss,2; ">
	Rate: 3
	Info: "Reduce damage from Boss by 2%"
},
{
	AegisName: "Kobold_Archer_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Cookie_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Quve_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Kraben_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Cramp_Card"
	Script: <" bonus bFlee,1; ">
	Rate: 3
	Info: "Flee +1"
},
{
	AegisName: "Cruiser_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Cremy_Fear_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Clock_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Chimera_Card"
	Script: <" bonus2 bSubEle,Ele_Fire,7; ">
	Rate: 3
	Info: "Fire Property defense +7%"
},
{
	AegisName: "Killer_Mantis_Card"
	Script: <" bonus2 bSubEle,Ele_Earth,3; ">
	Rate: 3
	Info: "Earth Property defense +3%"
},
{
	AegisName: "Tao_Gunka_Card"
	Script: <" bonus bMaxHPrate,7; ">
	Rate: 3
	Info: "Maximum HP +7%"
},
{
	AegisName: "Whisper_Boss_Card"
	Script: <" bonus bFlee,2; ">
	Rate: 3
	Info: "Flee +2"
},
{
	AegisName: "Tamruan_Card"
	Script: <" bonus2 bSkillAtk,CR_SHIELDBOOMERANG,10; ">
	Rate: 3
	Info: "Inflict 10% more damage with Shield Boomerang skills"
},
{
	AegisName: "Turtle_General_Card"
	Script: <" bonus bAtkRate,4; bonus bMatkRate,4; ">
	Rate: 3
	Info: "Atk & Matk +4%"
},
{
	AegisName: "Toad_Card"
	Script: <" bonus bFlee2,1; ">
	Rate: 3
	Info: "Perfect Dodge +1"
},
{
	AegisName: "Kind_Of_Beetle_Card"
	Script: <" bonus2 bAddRace,RC_Fish,1; ">
	Rate: 3
	Info: "Fish monster damage +1%"
},
{
	AegisName: "Tri_Joint_Card"
	Script: <" bonus2 bAddRace,RC_Formless,1; ">
	Rate: 3
	Info: "Formless monster damage +1%"
},
{
	AegisName: "Parasite_Card"
	Script: <" bonus2 bAddEle,Ele_Neutral,1; ">
	Rate: 3
	Info: "Neutral Property damage +1%"
},
{
	AegisName: "Panzer_Goblin_Card"
	Script: <" bonus bCritical,1; ">
	Rate: 3
	Info: "Crit +1"
},
{
	AegisName: "Permeter_Card"
	Script: <" bonus bBaseAtk,2; bonus bMatk,2; ">
	Rate: 3
	Info: "Atk & Matk +2"
},
{
	AegisName: "Fur_Seal_Card"
	Script: <" bonus bHit,1; bonus bFlee,1; ">
	Rate: 3
	Info: "Hit & Flee +1"
},
{
	AegisName: "Punk_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Penomena_Card"
	Script: <" bonus2 bAddRace,RC_Formless,1; ">
	Rate: 3
	Info: "Formless monster damage +1%"
},
{
	AegisName: "Pest_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Fake_Angel_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Mobster_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Knight_Windstorm_Card"
	Script: <" bonus2 bSkillAtk,WZ_STORMGUST,7; ">
	Rate: 3
	Info: "Storm Gust damage +7%"
},
{
	AegisName: "Freezer_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Bloody_Knight_Card"
	Script: <" bonus2 bSkillAtk,WZ_METEOR,2; ">
	Rate: 3
	Info: "Meteor Storm damage +2%"
},
{
	AegisName: "Hylozoist_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "High_Orc_Card"
	Script: <" bonus bShortWeaponDamageReturn,1; ">
	Rate: 3
	Info: "Physical reflect damage +1%"
},
{
	AegisName: "Garm_Baby_Card"
	Script: <" bonus bInt,3; ">
	Rate: 3
	Info: "Int +3"
},
{
	AegisName: "Garm_Card"
	Script: <" bonus bInt,5; ">
	Rate: 3
	Info: "Int +5"
},
{
	AegisName: "Harpy_Card"
	Script: <" bonus2 bAddEle,Ele_Neutral,1; ">
	Rate: 3
	Info: "Neutral Property damage +1%"
},
{
	AegisName: "See_Otter_Card"
	Script: <" bonus2 bAddEle,Ele_Water,1; ">
	Rate: 3
	Info: "Water Property damage +1%"
},
{
	AegisName: "Blood_Butterfly_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Hyegun_Card"
	Script: <" bonus bFlee,3; ">
	Rate: 3
	Info: "Flee +3"
},
{
	AegisName: "Phendark_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Dark_Snake_Lord_Card"
	Script: <" bonus bInt,5; ">
	Rate: 3
	Info: "Int +5"
},
{
	AegisName: "Heater_Card"
	Script: <" bonus bCritical,1; ">
	Rate: 3
	Info: "Crit +1"
},
{
	AegisName: "Waste_Stove_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Venomous_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Noxious_Card"
	Script: <" bonus bLongAtkDef,1; ">
	Rate: 3
	Info: "Long Range defense +1%"
},
{
	AegisName: "Pitman_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Ungoliant_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Porcellio_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Obsidian_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Mineral_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Teddy_Bear_Card"
	Script: <" bonus2 bAddEle,Ele_Undead,1; ">
	Rate: 3
	Info: "Undead Property damage +1%"
},
{
	AegisName: "Metaling_Card"
	Script: <" bonus2 bAddSize,Size_Small,1; ">
	Rate: 3
	Info: "Small monster damage +1%"
},
{
	AegisName: "Rsx_0806_Card"
	Script: <" bonus bNoKnockback,0; ">
	Rate: 3
	Info: "Gain immunity to Knockback"
},
{
	AegisName: "Mole_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Anopheles_Card"
	Script: <" bonus2 bAddSize,Size_Small,1; ">
	Rate: 3
	Info: "Small monster damage +1%"
},
{
	AegisName: "Hill_Wind_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Ygnizem_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Armaia_Card"
	Script: <" bonus2 bAddSize,Size_Medium,1; ">
	Rate: 3
	Info: "Medium monster damage +1%"
},
{
	AegisName: "Whikebain_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Erend_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Rawrel_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Kavac_Card"
	Script: <" bonus bFlee,2; ">
	Rate: 3
	Info: "Flee +2"
},
{
	AegisName: "B_Ygnizem_Card"
	Script: <" bonus bMaxHP,100; bonus bMaxSP,100; ">
	Rate: 3
	Info: "HP & SP +100"
},
{
	AegisName: "Removal_Card"
	Script: <" bonus bMaxHP,100; bonus bMaxSP,100; ">
	Rate: 3
	Info: "HP & SP +100"
},
{
	AegisName: "Gemini_Card"
	Script: <" bonus bMaxHP,100; bonus bMaxSP,100; ">
	Rate: 3
	Info: "HP & SP +100"
},
{
	AegisName: "Gremlin_Card"
	Script: <" bonus bMaxHP,100; bonus bMaxSP,100; ">
	Rate: 3
	Info: "HP & SP +100"
},
{
	AegisName: "Beholder_Card"
	Script: <" skill SA_CASTCANCEL,1; ">
	Rate: 3
	Info: "Enable use of Level 1 Cast Cancel"
},
{
	AegisName: "B_Seyren_Card"
	Script: <" bonus bMaxHPrate,10; ">
	Rate: 3
	Info: "Maximum HP +10%"
},
{
	AegisName: "Seyren_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "B_Eremes_Card"
	Script: <" skill AS_CLOAKING,3;; ">
	Rate: 3
	Info: "Enable use of Level 3 Cloaking"
},
{
	AegisName: "Eremes_Card"
	Script: <" bonus bCritAtkRate,1; ">
	Rate: 3
	Info: "Critical damage +1%"
},
{
	AegisName: "B_Harword_Card"
	Script: <" bonus bAtkRate,5; ">
	Rate: 3
	Info: "Atk +5%"
},
{
	AegisName: "Harword_Card"
	Script: <" bonus bHit,3; ">
	Rate: 3
	Info: "Hit +3"
},
{
	AegisName: "B_Magaleta_Card"
	Script: <" bonus5 bAutoSpellWhenHit,"MG_SAFETYWALL",4,50,BF_WEAPON|BF_MAGIC,0; ">
	Rate: 3
	Info: "Adds chance of autocasting Level 4 Safety Wall"
},
{
	AegisName: "Magaleta_Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "Vit +1"
},
{
	AegisName: "B_Katrinn_Card"
	Script: <" bonus bMatkRate,5; ">
	Rate: 3
	Info: "Matk +5%"
},
{
	AegisName: "Katrinn_Card"
	Script: <" bonus bInt,1; ">
	Rate: 3
	Info: "Int +1"
},
{
	AegisName: "B_Shecil_Card"
	Script: <" bonus bLongAtkRate,5; ">
	Rate: 3
	Info: "Long Range damage +5%"
},
{
	AegisName: "Shecil_Card"
	Script: <" bonus bDex,1; ">
	Rate: 3
	Info: "Dex +1"
},
{
	AegisName: "Venatu_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Dimik_Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "Vit +1"
},
{
	AegisName: "Archdam_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Bacsojin_Card"
	Script: <" bonus bHealPower,10; ">
	Rate: 3
	Info: "Heal Effectiveness + 10%"
},
{
	AegisName: "Chung_E_Card"
	Script: <" bonus bLuk,1; ">
	Rate: 3
	Info: "Luk +1"
},
{
	AegisName: "Apocalips_H_Card"
	Script: <" bonus bDex,2; ">
	Rate: 3
	Info: "Dex +2"
},
{
	AegisName: "Orc_Baby_Card_Card"
	Script: <" bonus bFlee,2; ">
	Rate: 3
	Info: "Flee +2"
},
{
	AegisName: "Lady_Tanee_Card"
	Script: <" bonus bMaxSPrate,2; ">
	Rate: 3
	Info: "SP +2%"
},
{
	AegisName: "Green_Iguana_Card"
	Script: <" bonus2 bAddRace,RC_Formless,1; ">
	Rate: 3
	Info: "Formless monster damage +1%"
},
{
	AegisName: "Acidus_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Acidus__Card"
	Script: <" bonus bMaxSP,100; ">
	Rate: 3
	Info: "SP +100"
},
{
	AegisName: "Ferus_Card"
	Script: <" bonus2 bAddEle,Ele_Fire,1; ">
	Rate: 3
	Info: "Fire Property damage +1%"
},
{
	AegisName: "Ferus__Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "Vit +1"
},
{
	AegisName: "Novus__Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Novus_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Hydro_Card"
	Script: <" bonus2 bAddSize,Size_Large,5; ">
	Rate: 3
	Info: "Large monster damage +5%"
},
{
	AegisName: "Dragon_Egg_Card"
	Script: <" bonus2 bAddEle,Ele_Neutral,1; ">
	Rate: 3
	Info: "Neutral Property damage +1%"
},
{
	AegisName: "Detale_Card"
	Script: <" bonus2 bAddSize,Size_Large,7; ">
	Rate: 3
	Info: "Large monster damage +7%"
},
{
	AegisName: "Ancient_Mimic_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Deathword_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Plasma_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Breeze_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Retribution_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Observation_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Shelter_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Solace_Card"
	Script: <" bonus bMaxHP,100; ">
	Rate: 3
	Info: "HP +100"
},
{
	AegisName: "Tha_Maero_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Tha_Odium_Card"
	Script: <" bonus bAgi,1; ">
	Rate: 3
	Info: "Agi +1"
},
{
	AegisName: "Tha_Despero_Card"
	Script: <" bonus bInt,1; ">
	Rate: 3
	Info: "Int +1"
},
{
	AegisName: "Tha_Dolor_Card"
	Script: <" bonus2 bSubRace,RC_Angel,1; ">
	Rate: 3
	Info: "Angel monster damage +1%"
},
{
	AegisName: "Thanatos_Card"
	Script: <" bonus bMaxHPrate,10; ">
	Rate: 3
	Info: "Maximum HP +10%"
},
{
	AegisName: "Aliza_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Alicel_Card"
	Script: <" bonus bFlee,2; ">
	Rate: 3
	Info: "Flee +2"
},
{
	AegisName: "Aliot_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Kiel_Card"
	Script: <" bonus bDelayrate,-15; ">
	Rate: 3
	Info: "Reduces all skill's after-cast delay by 15%"
},
{
	AegisName: "Skogul_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Frus_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Skeggiold_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Randgris_Card"
	Script: <" bonus3 bAutoSpell,SA_DISPELL,1,25; ">
	Rate: 3
	Info: "Adds autocasting Dispell on each physical attack"
},
{
	AegisName: "Gloom_Under_Night_Card"
	Script: <" bonus2 bAddRace,RC_Boss,10; ">
	Rate: 3
	Info: "Boss monster damage +10%"
},
{
	AegisName: "Agav_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Echio_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Vanberk_Card"
	Script: <" bonus bCritical,1; ">
	Rate: 3
	Info: "Crit +1"
},
{
	AegisName: "Isilla_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Hodremlin_Card"
	Script: <" bonus bFlee2,1; ">
	Rate: 3
	Info: "Perfect Dodge +1"
},
{
	AegisName: "Seeker_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Snowier_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Siroma_Card"
	Script: <" bonus2 bSkillAtk,MG_COLDBOLT,1; ">
	Rate: 3
	Info: "Cold Bolt damage +1%"
},
{
	AegisName: "Ice_Titan_Card"
	Script: <" bonus bVit,1; ">
	Rate: 3
	Info: "Vit +1"
},
{
	AegisName: "Gazeti_Card"
	Script: <" bonus2 bSkillAtk,MG_COLDBOLT,1; ">
	Rate: 3
	Info: "Cold Bolt damage +1%"
},
{
	AegisName: "Ktullanux_Card"
	Script: <" bonus2 bAddEle,Ele_Water,7; ">
	Rate: 3
	Info: "Water Property damage +7%"
},
{
	AegisName: "Muscipular_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Drosera_Card"
	Script: <" bonus bCritical,1; ">
	Rate: 3
	Info: "Crit +1"
},
{
	AegisName: "Roween_Card"
	Script: <" bonus bFlee,2; ">
	Rate: 3
	Info: "Flee +2"
},
{
	AegisName: "Galion_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Stapo_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Atroce_Card"
	Script: <" bonus2 bAddSize,Size_Large,5; ">
	Rate: 3
	Info: "Large monster damage +5%"
},
{
	AegisName: "Byorgue_Card"
	Script: <" bonus bBaseAtk,5; bonus bMatk,5; ">
	Rate: 3
	Info: "Atk & Matk +5"
},
{
	AegisName: "Sword_Guardian_Card"
	Script: <" bonus2 bSkillAtk,62,5; ">
	Rate: 3
	Info: "Bowling Bash damage +5%"
},
{
	AegisName: "Bow_Guardian_Card"
	Script: <" bonus2 bSkillAtk,46,5; ">
	Rate: 3
	Info: "Double Strafe damage +5%"
},
{
	AegisName: "Salamander_Card"
	Script: <" bonus2 bSkillAtk,WZ_METEOR,1; ">
	Rate: 3
	Info: "Meteor Storm damage +1%"
},
{
	AegisName: "Ifrit_Card"
	Script: <" bonus bInt,2; bonus bStr,2; bonus bDex,2; bonus bLuk,2; ">
	Rate: 3
	Info: "STR, INT, DEX & LUK +2"
},
{
	AegisName: "Kasa_Card"
	Script: <" bonus2 bSkillAtk,MG_FIREBOLT,1; ">
	Rate: 3
	Info: "Fire Bolt damage +1%"
},
{
	AegisName: "Magmaring_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
{
	AegisName: "Imp_Card"
	Script: <" bonus2 bSkillAtk,MG_FIREBOLT,1; ">
	Rate: 3
	Info: "Fire Bolt damage +1%"
},
{
	AegisName: "Knocker_Card"
	Script: <" bonus2 bAddRace,RC_Formless,1; ">
	Rate: 3
	Info: "Formless monster damage +1%"
},
{
	AegisName: "Zombie_Slaughter_Card"
	Script: <" bonus2 bAddRace,RC_DemiPlayer,1; ">
	Rate: 3
	Info: "Demihuman monster damage +1%"
},
{
	AegisName: "Ragged_Zombie_Card"
	Script: <" bonus2 bAddRace,RC_DemiPlayer,1; ">
	Rate: 3
	Info: "Demihuman monster damage +1%"
},
{
	AegisName: "Hell_Poodle_Card"
	Script: <" bonus bHit,2; ">
	Rate: 3
	Info: "Hit +2"
},
{
	AegisName: "Banshee_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Flame_Skull_Card"
	Script: <" bonus bAddMaxWeight,300; ">
	Rate: 3
	Info: "Weight +30"
},
{
	AegisName: "Necromancer_Card"
	Script: <" bonus bInt,1; ">
	Rate: 3
	Info: "Int +1"
},
{
	AegisName: "Fallen_Bishop_Card"
	Script: <" bonus bMatkRate,5; ">
	Rate: 3
	Info: "Matk +5%"
},
{
	AegisName: "Tatacho_Card"
	Script: <" bonus2 bAddEle,Ele_Neutral,1; ">
	Rate: 3
	Info: "Neutral Property damage +1%"
},
{
	AegisName: "Aqua_Elemental_Card"
	Script: <" bonus2 bAddEle,Ele_Water,1; ">
	Rate: 3
	Info: "Water Property damage +1%"
},
{
	AegisName: "Draco_Card"
	Script: <" bonus2 bAddEle,Ele_Earth,1; ">
	Rate: 3
	Info: "Earth Property damage +1%"
},
{
	AegisName: "Luciola_Vespa_Card"
	Script: <" bonus2 bAddEle,Ele_Wind,1; ">
	Rate: 3
	Info: "Wind Property damage +1%"
},
{
	AegisName: "Centipede_Card"
	Script: <" bonus2 bAddEle,Ele_Poison,1; ">
	Rate: 3
	Info: "Poison Property damage +1%"
},
{
	AegisName: "Cornus_Card"
	Script: <" bonus2 bAddEle,Ele_Holy,1; ">
	Rate: 3
	Info: "Holy Property damage +1%"
},
{
	AegisName: "Dark_Shadow_Card"
	Script: <" bonus2 bAddEle,Ele_Dark,1; ">
	Rate: 3
	Info: "Dark Property damage +1%"
},
{
	AegisName: "Banshee_Master_Card"
	Script: <" bonus bInt,1; ">
	Rate: 3
	Info: "Int +1"
},
{
	AegisName: "Ant_Buyanne_Card"
	Script: <" bonus bMatkRate,5; ">
	Rate: 3
	Info: "Matk +5%"
},
{
	AegisName: "Centipede_Larva_Card"
	Script: <" bonus bMatk,3; ">
	Rate: 3
	Info: "Matk +3"
},
{
	AegisName: "Hilsrion_Card"
	Script: <" bonus bBaseAtk,3; ">
	Rate: 3
	Info: "Atk +3"
},
)