# TN Skills Documentation

This document contains detailed information about all TN (Taekwon Ninja) skills found in the codebase, including their implementations, effects, and technical specifications.

## Overview

The TN skills are custom skills with IDs ranging from 2836-2849. They are quest-type skills with various effects including damage, utility, and status changes. All TN skills have a maximum level of 1 and most have a 60-second cooldown.

## Skill Definitions

### TN_EXECUTE (ID: 2836)
**Description:** Execute
**Type:** Enemy-targeted damage skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2836
	Name: "TN_EXECUTE"
	Description: "Execute"
	MaxLevel: 1
	Range: 5
	Hit: "BDT_SKILL"
	SkillType: {
		Enemy: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	DamageType: {
		IgnoreFlee: true
	}
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Single-target enemy skill with 5-cell range
- Ignores flee (guaranteed hit)
- 60-second cooldown
- Misc attack type (not weapon or magic)

### TN_FLICKER (ID: 2837)
**Description:** Flicker
**Type:** Ground-targeted teleportation skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2837
	Name: "TN_FLICKER"
	Description: "Flicker"
	MaxLevel: 1
	Range: 25
	Hit: "BDT_SKILL"
	SkillType: {
		Place: true
	}
	SkillInfo: {
		Quest: true
	}
	DamageType: {
		NoDamage: true
	}
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Ground-targeted skill with 25-cell range
- No damage (utility skill)
- Likely used for teleportation/movement
- 60-second cooldown

### TN_HEAL (ID: 2838)
**Description:** Heal
**Type:** Self-targeted healing skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2838
	Name: "TN_HEAL"
	Description: "Heal"
	MaxLevel: 1
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Holy"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 15
	InterruptCast: true
	SkillData1: 10000
	CoolDown: 12000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted healing skill
- Holy element with 15-cell splash range
- 10-second duration (SkillData1: 10000ms)
- 12-second cooldown (shorter than other TN skills)
- Can be interrupted during casting
- Area of effect healing

### TN_PURIFY (ID: 2839)
**Description:** Purify
**Type:** Self-targeted cleansing skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2839
	Name: "TN_PURIFY"
	Description: "Purify"
	MaxLevel: 1
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Holy"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 15
	InterruptCast: true
	SkillData1: 3000
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted purification skill
- Holy element with 15-cell splash range
- 3-second duration (SkillData1: 3000ms)
- 60-second cooldown
- Can be interrupted during casting
- Likely removes debuffs/negative status effects

**Status Change Implementation:**
<augment_code_snippet path="src/map/status.h" mode="EXCERPT">
````c
SC_TN_PURIFY,
````
</augment_code_snippet>

### TN_ROAR (ID: 2840)
**Description:** Roar
**Type:** Self-targeted area effect skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2840
	Name: "TN_ROAR"
	Description: "Roar"
	MaxLevel: 1
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Holy"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 15
	InterruptCast: true
	SkillData1: 10000
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted skill with large area effect
- Holy element with 15-cell splash range
- 10-second duration (SkillData1: 10000ms)
- 60-second cooldown
- Can be interrupted during casting
- Likely provides area buffs or intimidation effects

**Status Change Implementation:**
<augment_code_snippet path="src/map/status.h" mode="EXCERPT">
````c
SC_TN_ROAR,
````
</augment_code_snippet>

### TN_RETRIBUTION (ID: 2841)
**Description:** Retribution
**Type:** Self-targeted counter-attack skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2841
	Name: "TN_RETRIBUTION"
	Description: "Retribution"
	MaxLevel: 1
	Range: 4
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Random"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 4
	SkillData1: 10000
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted skill with 4-cell range and splash
- Random element
- 10-second duration (SkillData1: 10000ms)
- 60-second cooldown
- Likely provides counter-attack or damage reflection

**Status Change Implementation:**
<augment_code_snippet path="src/map/status.h" mode="EXCERPT">
````c
SC_TN_RETRIBUTION,
````
</augment_code_snippet>

### TN_AEGIS (ID: 2842)
**Description:** Aegis
**Type:** Self-targeted defensive skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2842
	Name: "TN_AEGIS"
	Description: "Aegis"
	MaxLevel: 1
	Range: 7
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Random"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 4
	SkillData1: 3000
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted defensive skill with 7-cell range
- Random element with 4-cell splash
- 3-second duration (SkillData1: 3000ms)
- 60-second cooldown
- Provides damage reduction

**Status Change Implementation:**
<augment_code_snippet path="src/map/status.h" mode="EXCERPT">
````c
SC_TN_AEGIS,
````
</augment_code_snippet>

**Battle System Integration:**
<augment_code_snippet path="src/map/battle.c" mode="EXCERPT">
````c
if (tsd->sc.data[SC_TN_AEGIS])
	cardfix = cardfix * (100 - tsd->sc.data[SC_TN_AEGIS]->val1) / 100;
````
</augment_code_snippet>

The SC_TN_AEGIS status reduces incoming damage by a percentage stored in val1.

### TN_DOUBT (ID: 2843)
**Description:** Doubt
**Type:** Self-targeted debuff skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2843
	Name: "TN_DOUBT"
	Description: "Doubt"
	MaxLevel: 1
	Range: 7
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Random"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 4
	SkillData1: 30000
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted skill with 7-cell range
- Random element with 4-cell splash
- 30-second duration (SkillData1: 30000ms) - longest duration among TN skills
- 60-second cooldown
- Likely inflicts confusion or accuracy reduction on enemies

### TN_STEALTH (ID: 2844)
**Description:** Stealth
**Type:** Self-targeted invisibility skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2844
	Name: "TN_STEALTH"
	Description: "Stealth"
	MaxLevel: 1
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Holy"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 15
	InterruptCast: true
	SkillData1: 5000
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted invisibility skill
- Holy element with 15-cell splash range
- 5-second duration (SkillData1: 5000ms)
- 60-second cooldown
- Can be interrupted during casting
- Provides stealth/hiding effect

**Status Change Implementation:**
<augment_code_snippet path="src/map/status.h" mode="EXCERPT">
````c
SC_TN_STEALTH,
````
</augment_code_snippet>

### TN_SUBSTITUTION (ID: 2845)
**Description:** Substitution
**Type:** Self-targeted evasion skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2845
	Name: "TN_SUBSTITUTION"
	Description: "Substitution"
	MaxLevel: 1
	Range: 7
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Random"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 4
	SkillData1: 5000
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted skill with 7-cell range
- Random element with 4-cell splash
- 5-second duration (SkillData1: 5000ms)
- 60-second cooldown
- Likely provides damage avoidance or creates decoy

**Status Change Implementation:**
<augment_code_snippet path="src/map/status.h" mode="EXCERPT">
````c
SC_TN_SUBSTITUTION,
````
</augment_code_snippet>

**Battle System Integration:**
<augment_code_snippet path="src/map/battle.c" mode="EXCERPT">
````c
if (target->type == BL_PC && tsc && tsc->data[SC_TN_SUBSTITUTION]) {
	int swapx = src->x + 1, swapy = src->y;
	if (unit->movepos(target, swapx, swapy, 1, 1)) {
#if PACKETVER >= 20111005
		clif->slide(target, swapx, swapy);
#endif
		clif->specialeffect(target, 304, AREA);
	}
}
````
</augment_code_snippet>

The SC_TN_SUBSTITUTION status triggers a position swap when the target is attacked, moving them to an adjacent position with visual effects.

### TN_SWAP (ID: 2846)
**Description:** Swap
**Type:** Enemy-targeted position exchange skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2846
	Name: "TN_SWAP"
	Description: "Swap"
	MaxLevel: 1
	Range: 18
	Hit: "BDT_SKILL"
	SkillType: {
		Enemy: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	DamageType: {
		NoDamage: true
	}
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Enemy-targeted skill with 18-cell range (longest range among TN skills)
- No damage (utility skill)
- 60-second cooldown
- Swaps positions with target enemy

### TN_DAZE (ID: 2847)
**Description:** Daze
**Type:** Self-targeted stunning skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2847
	Name: "TN_DAZE"
	Description: "Daze"
	MaxLevel: 1
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Random"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 1
	SkillData1: 1500
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted skill with small 1-cell splash
- Random element
- 1.5-second duration (SkillData1: 1500ms) - shortest duration
- 60-second cooldown
- Likely stuns or dazes nearby enemies

### TN_DISRUPT (ID: 2848)
**Description:** Disrupt
**Type:** Self-targeted knockback skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2848
	Name: "TN_DISRUPT"
	Description: "Disrupt"
	MaxLevel: 1
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Random"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 1
	KnockBackTiles: 5
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted skill with 1-cell splash
- Random element
- Knocks back targets 5 tiles
- 60-second cooldown
- Disrupts enemy positioning

**Battle System Integration:**
<augment_code_snippet path="src/map/battle.c" mode="EXCERPT">
````c
case TN_DISRUPT:
	state |= BCT_ENEMY;
	strip_enemy = 0;
	break;
````
</augment_code_snippet>

TN_DISRUPT is recognized in the battle system as an enemy-targeting skill.

### TN_REDIRECT (ID: 2849)
**Description:** Redirect
**Type:** Self-targeted redirection skill

<augment_code_snippet path="db/pre-re/skill_db.conf" mode="EXCERPT">
````conf
{
	Id: 2849
	Name: "TN_REDIRECT"
	Description: "Redirect"
	MaxLevel: 1
	Range: 7
	Hit: "BDT_SKILL"
	SkillType: {
		Self: true
	}
	SkillInfo: {
		Quest: true
	}
	AttackType: "Misc"
	Element: "Ele_Random"
	DamageType: {
		NoDamage: true
		SplashArea: true
	}
	SplashRange: 4
	SkillData1: 3000
	CoolDown: 60000
}
````
</augment_code_snippet>

**Effects:**
- Self-targeted skill with 7-cell range
- Random element with 4-cell splash
- 3-second duration (SkillData1: 3000ms)
- 60-second cooldown
- Redirects attacks or targeting

**Status Change Implementation:**
<augment_code_snippet path="src/map/status.h" mode="EXCERPT">
````c
SC_REDIRECT,
````
</augment_code_snippet>

**Battle System Integration:**
<augment_code_snippet path="src/map/battle.c" mode="EXCERPT">
````c
if (tsc->data[SC_REDIRECT])
	return -1;
````
</augment_code_snippet>

The SC_REDIRECT status makes the target untargetable, effectively redirecting attacks away.

## Technical Implementation

### Skill ID Definitions
<augment_code_snippet path="src/map/skill.h" mode="EXCERPT">
````c
TN_DAZE = 2847,
TN_DISRUPT,
TN_REDIRECT,
TN_EXECUTE = 2836,
TN_FLICKER,
TN_HEAL,
TN_PURIFY,
TN_ROAR,
TN_RETRIBUTION,
TN_AEGIS,
TN_DOUBT,
TN_STEALTH,
TN_SUBSTITUTION,
TN_SWAP,
````
</augment_code_snippet>

### Status Change Definitions
<augment_code_snippet path="src/map/status.h" mode="EXCERPT">
````c
SC_REDIRECT,
SC_TN_ROAR,
SC_TN_RETRIBUTION,
SC_TN_AEGIS,
SC_TN_SUBSTITUTION,
SC_TN_STEALTH,
SC_TN_PURIFY,
````
</augment_code_snippet>

### Additional Battle System Integration

The TN skills are integrated into various parts of the battle system:

<augment_code_snippet path="src/map/battle.c" mode="EXCERPT">
````c
// Multiple instances of TN_AEGIS damage reduction
if (tsd->sc.data[SC_TN_AEGIS])
	cardfix = cardfix * (100 - tsd->sc.data[SC_TN_AEGIS]->val1) / 100;
````
</augment_code_snippet>

TN_AEGIS damage reduction is applied in multiple damage calculation functions, showing its comprehensive defensive capabilities.

### Skill Learning Integration

<augment_code_snippet path="src/map/pc.c" mode="EXCERPT">
````c
if( (skill->dbs->db[idx].inf2&(INF2_QUEST_SKILL|INF2_WEDDING_SKILL)) )
	continue; //Do not include Quest/Wedding skills.
````
</augment_code_snippet>

TN skills are marked as quest skills (INF2_QUEST_SKILL), meaning they cannot be learned through normal skill point allocation and require special quest completion or other acquisition methods.

## Summary

The TN skills represent a comprehensive ninja-themed skill set with the following characteristics:

1. **All skills are quest-type** - requiring special acquisition methods
2. **Maximum level of 1** - simple, binary effects
3. **Most have 60-second cooldowns** - except TN_HEAL (12 seconds)
4. **Mix of utility and combat skills** - from healing to position swapping
5. **Various ranges** - from 1-cell splash to 25-cell range
6. **Element diversity** - Holy, Random, and unspecified elements
7. **Status change integration** - Several skills create lasting effects
8. **Battle system integration** - Some skills have special targeting rules

### Skill Categories by Function:

**Offensive Skills:**
- TN_EXECUTE (ID: 2836) - Direct damage with guaranteed hit

**Defensive Skills:**
- TN_AEGIS (ID: 2842) - Damage reduction
- TN_SUBSTITUTION (ID: 2845) - Evasion with position swap

**Utility Skills:**
- TN_FLICKER (ID: 2837) - Teleportation
- TN_SWAP (ID: 2846) - Position exchange with enemy
- TN_STEALTH (ID: 2844) - Invisibility

**Support Skills:**
- TN_HEAL (ID: 2838) - Area healing
- TN_PURIFY (ID: 2839) - Debuff removal
- TN_ROAR (ID: 2840) - Area buff/intimidation

**Control Skills:**
- TN_DAZE (ID: 2847) - Stunning
- TN_DISRUPT (ID: 2848) - Knockback
- TN_DOUBT (ID: 2843) - Confusion/accuracy reduction
- TN_REDIRECT (ID: 2849) - Attack redirection
- TN_RETRIBUTION (ID: 2841) - Counter-attack

The skills appear designed for tactical gameplay, offering utilities like stealth, healing, position manipulation, and defensive buffs rather than raw damage output. They provide a complete toolkit for ninja-style combat with emphasis on mobility, deception, and tactical advantages.