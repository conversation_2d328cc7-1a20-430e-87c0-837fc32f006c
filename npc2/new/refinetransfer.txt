prontera,226,212,5	script	Refine Transfer	4_F_JOB_BLACKSMITH,{
	goto OnTalk;
	end;

	function check_bound_rental;
	function check_whitelist;

OnInit:
	.Refine_Zeny_Fee = 300000000;
	.Refine_Item_Fee = 3165;
	.Refine_Item_Amount = 1;
	.Enchant_Zeny_Fee = 300000000;
	.Enchant_Item_Fee = 3165;
	.Enchant_Item_Amount = 1;
	setarray .costume_slots[0], EQI_COSTUME_HEAD_TOP, EQI_COSTUME_HEAD_MID, EQI_COSTUME_HEAD_LOW, EQI_COSTUME_GARMENT, EQI_SHADOW_ARMOR, EQI_SHADOW_WEAPON, EQI_SHADOW_SHIELD, EQI_SHADOW_SHOES, EQI_SHADOW_ACC_R, EQI_SHADOW_ACC_L;
	setarray .whitelist[0], 2115,2357,2410,2421,2423,2524,2541,2554,2647,2720,2721,2722,2723,2724,2725,3004,3017,3019,3030,3033,3036,3037,3038,3039,3040,3041,3042,3043,3460,3461,3462,3463,3464,3465,3466,3467,3468,3469,3470,3471,3472,3473,3474,3475,3476,3477,3478,3479,3480,3481,3482,3483,3484,3485,3486,3487,3488,3489,3490,3491,3492,3515,3516,3517,3518,3519,3520,3521,3522,3523,3524,3525,3526,3557,3558,3559,3560,3561,3562,3563,3564,3639,3640,3665,3666,3667,3668,3746,3747,3880,3881,3902,5135,5360,5788,8087,8088,8089,8090,8142,8212,8213,8246,8247,8423,8424,8425,8426,8427,8428,8429,8430,8431,8432,8433,8434,8435,8436,8437,8438,8439,8440,8607,8766,8767,8768,8769,8868,8962,13414,18600,20160,20161,20162,20163,20164,20165,20166,20167,20168,20701,20702,20708,20712,20713,20714,20715,20716,20717,20718,20722,20723,20724,20725,20726,20727,20728,20729,20770,20771,20772,20878,20879,20880,20881,20882,20883,20884,20885,21005,21054,21055,21056,21257,21395,21396,21401,21402,21403,21404,21444,21445,21446,21535,21536,21537,21538,21539,21540,21541,21542,21543,21544,21545,21546,21931,21932,21933,21934,21935,21936,21959,21960,21961,22017,22018,22019,30002,30003,30004,30005,30006,30007,30008,30009,30010,30011,30012,30018,30019,30020,30021,30022,30023,30024,30025,30028,30029,30031,30032,30033,30034,30036,30037,30038,30040,30041,30046,30047,30048,30049,30050,30051,30052,30053,30054,30055,30056,30057,30058,30059,30060,30061,30062,30063,30064,30065,30066,30068,30069,30070,30071,30072,30092,30093,30094,30095,30096,30097,30098,30099,30149,30150,30151,30152,30153,30154,30155,30156,30157,30158,30159,30165,30166,30167,30168,30169,30170,30171,30172,30175,30176,30178,30179,30180,30181,30183,30184,30185,30187,30188,30189,30190,30191,30192,30193,30194,30195,30196,30197,30198,30199,30200,30211,30212,30213,30214,30215,30216,30217,30218,30219,30220,30221,30222,30223,30224,30231,30232,30233,30234,30235,30236,30237,30238,30239,30240,30241,30242,30243,30244,30245,30246,30247,30251,30389,30390,30391,30392,30393,30394,30395,30396,30397,30398,30399,30400,30401,30402,30403,30404,30405,30406,30407,30420,30421,30422,30423,30424,30425,30426,30427,30428,30429,30430,30431,30432,30433,30434,30438,30439,30440,30441,30442,30443,30444,30445,30446,30447,30448,30449,30450,30451,30452,30453,30454,30455,30456,30457,30458,30459,30460,30461,30462,30463,30464,30465,30466,30467,30468,30469,30470,30471,30472,30473,30474,30475,30476,30477,30478,30479,30480,30481,40084,40085,40086,40087,40088,40089,40090,40091,40092,40093,40094,40095,40100,40101,40102,40103,40104,40105,8140,8143,30482,22020,22021,22022,22023; // Example item IDs
	end;

	function check_bound_rental {
		if (countbound() > 0 || countrental() > 0) {
			mes "[ ^996600Refine Transfer^000000 ]";
			mes "-------------------------------";
			mes "Please make sure you are not carrying bound or rental items.";
			close;
		}
		return;
	}

	function check_whitelist {
		for (.@i = 0; .@i < getarraysize(.whitelist); .@i++) {
			if (getequipid(getarg(0)) == .whitelist[.@i]) {
				return 1; // Return 1 (true) if item is in whitelist
			}
		}
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "I'm sorry, but this item is not eligible for transfer. Please try another one.";
		close;
		return 0; // This line is never reached due to close, but kept for clarity
	}

OnTalk:
	disable_items;
	mes "[ ^996600Refine Transfer^000000 ]";
	if (stashed_refine > 0 || stashed_orb_id > 0) {
		mes "-------------------------------";
		mes "You have a pending stashed ^777777refine level^000000 or ^777777enchant orb^000000.";
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "From Item: " + getitemname(stashed_item_id);
		if ($testserver == 1) {
			dispbottom "Debug: stashed_item_id = " + stashed_item_id;
			dispbottom "Debug: stashed_item_loc = " + stashed_item_loc;
		}
		if (stashed_refine > 0) {
			mes "Refine level: " + stashed_refine;
		} else if (stashed_orb_id > 0) {
			mes "Enchant Orb: " + getitemname(stashed_orb_id);
		}
		mes " ";
		mes "Would you like to apply it to another equipment?";
		if (select("[-] Yes please!:[-] Give me a second..") == 1) {
			callsub(ApplyStashed);
			close;
		} else {
			next;
			mes "[ ^996600Refine Transfer^000000 ]";
			mes "-------------------------------";
			mes "Please make sure to apply the stashed refine or orb before trying to transfer again.";
			close;
		}
	}
	mes "-------------------------------";
	mes "I can transfer refine levels and enchant orbs between items.";
	next;
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	mes "What would you like to transfer?";
	switch(select("[-] Refine Level:[-] Enchant Orb:[-] Information:[-] How it works?:[-] Whitelist Items")) {
		case 1:
			callsub(TransferRefine);
			break;
		case 2:
			callsub(TransferEnchantOrb);
			break;
		case 3:
			callsub(Information);
			break;
		case 4:
			callsub(HowItWorks);
			break;
		case 5:
			callsub(DisplayWhitelist);
			break;
	}

	close;

Information:
	next;
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	mes "Refine Transfer allows you to transfer ^777777refine levels^000000 and ^777777enchant orbs^000000 between equipments.";
	mes "And you can only transfer to a compatible item.";
	next;
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	mes "EG. ^777777+10 Armor^000000 to any Armor, ^777777+10 Garment^000000 to any Garment.";
	mes "-------------------------------";
	mes "For ^777777weapons^000000 the transfer must be to the same ^777777weapon level^000000.";
	next;
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	mes "As for ^777777restrictions^000000: you cannot transfer to bound or rental items.";
	next;
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	mes "And you will be charged a fee for every transfer you made.";
	enable_items;
	close;

HowItWorks:
	next;
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	mes "1. Select the item to transfer the refine or enchant from.";
	mes "2. Pay required fees.";
	mes "3. Wear the item to transfer the refine level or enchant orb to.";
	mes "4. Confirm the transfer and done!";
	enable_items;
	close;

ApplyStashed:
	next;
	.@to_slot = stashed_item_loc;
	set .@to_item_id, getequipid(.@to_slot);

	if ($testserver == 1) {
		dispbottom "Debug: Applying stashed item";
		dispbottom "Debug: to_slot = " + .@to_slot;
		dispbottom "Debug: to_item_id = " +  .@to_item_id;
	}
	if (.@to_item_id == -1) {
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "You don't have an item equipped in the required slot.";
		close;
	}
	if (!check_whitelist(.@to_slot)) end;
	check_bound_rental();

	// Confirmation message
	//mes "You have a pending stashed ^777777refine level^000000 or ^777777enchant orb^000000.";
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	if (stashed_refine > 0) {
		mes "Refine level: " + stashed_refine;
	} else if (stashed_orb_id > 0) {
		mes "Enchant Orb: " + getitemname(stashed_orb_id);
	}
	mes "Do you want to apply it to";
	mes " > ^777777" + getitemname(.@to_item_id) +"^000000?";
	if (select("[-] Yes please!:[-] Give me a second..") == 2) {
		mes " ";
		mes "^FF0000Operation cancelled.^000000";
		close;
	}

	if (stashed_refine > 0) {
		if (getiteminfo(.@to_item_id, ITEMINFO_TYPE) != getiteminfo(stashed_item_id, ITEMINFO_TYPE) ||
			getiteminfo(.@to_item_id, ITEMINFO_LOC) != getiteminfo(stashed_item_id, ITEMINFO_LOC) ||
			getiteminfo(.@to_item_id, ITEMINFO_WLV) != getiteminfo(stashed_item_id, ITEMINFO_WLV)) {
			next;
			mes "[ ^996600Refine Transfer^000000 ]";
			mes "-------------------------------";
			mes "The target item is not compatible.";
			close;
		}
		if (getequiprefinerycnt(.@to_slot) > 0) {
			next;
			mes "[ ^996600Refine Transfer^000000 ]";
			mes "-------------------------------";
			mes "The target item already has a refine level. Please equip an item with no refine level.";
			close;
		}
		successrefitem .@to_slot, stashed_refine;
		mes " ";
		mes "^008000Refine level applied successfully!^000000";
		set stashed_refine, 0;
		set stashed_item_id, 0;
		set stashed_item_loc, 0;
	} else if (stashed_orb_id > 0) {
		if (getiteminfo(.@to_item_id, ITEMINFO_TYPE) != getiteminfo(stashed_item_id, ITEMINFO_TYPE) ||
			getiteminfo(.@to_item_id, ITEMINFO_LOC) != getiteminfo(stashed_item_id, ITEMINFO_LOC) ||
			getiteminfo(.@to_item_id, ITEMINFO_WLV) != getiteminfo(stashed_item_id, ITEMINFO_WLV)) {
			next;
			mes "[ ^996600Refine Transfer^000000 ]";
			mes "-------------------------------";
			mes "The target item is not compatible.";
			close;
		}
		if (getequipcardid(.@to_slot, 3) != 0) { // 4th slot is index 3
			next;
			mes "[ ^996600Refine Transfer^000000 ]";
			mes "-------------------------------";
			mes "The target item already has an enchant orb. Please equip an item with no enchant orb.";
			close;
		}
		if (getiteminfo(.@to_item_id, ITEMINFO_LOC) != getiteminfo(stashed_item_id, ITEMINFO_LOC)) {
			next;
			mes "[ ^996600Refine Transfer^000000 ]";
			mes "-------------------------------";
			mes "The target item must be of the same costume location.";
			close;
		}

		// Save item data before deletion
		set .@card1, getequipcardid(.@to_slot, 0);
		set .@card2, getequipcardid(.@to_slot, 1);
		set .@card3, getequipcardid(.@to_slot, 2);
		set .@card4, getequipcardid(.@to_slot, 3);

		// save options
		@enchant1_type = getequipoption(.@to_slot,1,IT_OPT_INDEX);
		@enchant1_value = getequipoption(.@to_slot,1,IT_OPT_VALUE);
		@enchant2_type = getequipoption(.@to_slot,2,IT_OPT_INDEX);
		@enchant2_value = getequipoption(.@to_slot,2,IT_OPT_VALUE);
		@enchant3_type = getequipoption(.@to_slot,3,IT_OPT_INDEX);
		@enchant3_value = getequipoption(.@to_slot,3,IT_OPT_VALUE);
		@enchant4_type = getequipoption(.@to_slot,4,IT_OPT_INDEX);
		@enchant4_value = getequipoption(.@to_slot,4,IT_OPT_VALUE);
		@enchant5_type = getequipoption(.@to_slot,5,IT_OPT_INDEX);
		@enchant5_value = getequipoption(.@to_slot,5,IT_OPT_VALUE);

		set .@refine_level, getequiprefinerycnt(.@to_slot);

		delequip(.@to_slot);
		getitem2 .@to_item_id, 1, 1, .@refine_level, 0, .@card1, .@card2, .@card3, stashed_orb_id;

		// Re apply options if any
		if (@enchant1_type) {
			equip2 .@to_item_id, .@refine_level, 0, .@card1, .@card2, .@card3, stashed_orb_id;
			setequipoption .@to_slot, 1, @enchant1_type, @enchant1_value;
		}
		if (@enchant2_type) {
			equip2 .@to_item_id, .@refine_level, 0, .@card1, .@card2, .@card3, stashed_orb_id;
			setequipoption .@to_slot, 2, @enchant2_type, @enchant2_value;
		}
		if (@enchant3_type) {
			equip2 .@to_item_id, .@refine_level, 0, .@card1, .@card2, .@card3, stashed_orb_id;
			setequipoption .@to_slot, 3, @enchant3_type, @enchant3_value;
		}
		if (@enchant4_type) {
			equip2 .@to_item_id, .@refine_level, 0, .@card1, .@card2, .@card3, stashed_orb_id;
			setequipoption .@to_slot, 4, @enchant4_type, @enchant4_value;
		}
		if (@enchant5_type) {
			equip2 .@to_item_id, .@refine_level, 0, .@card1, .@card2, .@card3, stashed_orb_id;
			setequipoption .@to_slot, 5, @enchant5_type, @enchant5_value;
		}

		successrefitem .@to_slot, .@refine_level;

		mes " ";
		mes "^008800Enchant Orb applied successfully!^000000";
		set stashed_orb_id, 0;
		set stashed_item_id, 0;
		set stashed_item_loc, 0;
	}
	enable_items;
	close;

TransferRefine:
	next;
	check_bound_rental();
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	mes "Select the item to transfer the refine level from:";
	setarray .@position$[1], "Top", "Armor", "Left hand", "Right hand", "Garment", "Shoes", "Accessory 1", "Accessory 2", "Head Mid", "Head Low";
	.@menu$ = "";
	for (.@i = 1; .@i <= 20; ++.@i) {
		if (getequipisequiped(.@i)) {
			.@menu$ += .@position$[.@i] + " - [" + getequipname(.@i) + "]";
			.@equipped = 1;
		}
		.@menu$ += ":";
	}
	if (.@equipped == 0) {
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "I don't think I can transfer any enchant orbs from the items you have equipped.";
		close;
	}
	.@part = select(.@menu$);
	set .@from_slot, .@part;
	if (!check_whitelist(.@from_slot)) end;
	check_bound_rental();
	set .@refine_level, getequiprefinerycnt(.@from_slot);
	if (.@refine_level == 0) {
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "This item has no refine level.";
		close;
	}
	//mes "The transfer will cost " + .Refine_Zeny_Fee + " Zeny and " + .Refine_Item_Amount + " Refine Transfer Scroll(s).";
	if (Zeny < .Refine_Zeny_Fee || countitem(.Refine_Item_Fee) < .Refine_Item_Amount) {
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "You do not have enough Zeny or the required item.";
		mes " ";
		mes "Zeny: " + Zeny + " / " + .Refine_Zeny_Fee;
		mes "Transfer Scroll: " + countitem(.Refine_Item_Fee) + " / " + .Refine_Item_Amount;
		close;
	}
	Zeny -= .Refine_Zeny_Fee;
	delitem .Refine_Item_Fee, .Refine_Item_Amount;
	 // Save item data before deletion
	set .@item_id, getequipid(.@from_slot);
	set .@card1, getequipcardid(.@from_slot, 0);
	set .@card2, getequipcardid(.@from_slot, 1);
	set .@card3, getequipcardid(.@from_slot, 2);
	set .@card4, getequipcardid(.@from_slot, 3);


	// save options
	@enchant1_type = getequipoption(.@from_slot,1,IT_OPT_INDEX);
	@enchant1_value = getequipoption(.@from_slot,1,IT_OPT_VALUE);
	@enchant2_type = getequipoption(.@from_slot,2,IT_OPT_INDEX);
	@enchant2_value = getequipoption(.@from_slot,2,IT_OPT_VALUE);
	@enchant3_type = getequipoption(.@from_slot,3,IT_OPT_INDEX);
	@enchant3_value = getequipoption(.@from_slot,3,IT_OPT_VALUE);
	@enchant4_type = getequipoption(.@from_slot,4,IT_OPT_INDEX);
	@enchant4_value = getequipoption(.@from_slot,4,IT_OPT_VALUE);
	@enchant5_type = getequipoption(.@from_slot,5,IT_OPT_INDEX);
	@enchant5_value = getequipoption(.@from_slot,5,IT_OPT_VALUE);

	// Remove refine from the origin item
	delequip(.@from_slot);
	getitem2 .@item_id, 1, 1, 0, 0, .@card1, .@card2, .@card3, .@card4;

	// Re apply options if any
	if (@enchant1_type) {
		equip2 .@item_id, 0, 0, .@card1, .@card2, .@card3, .@card4;
		setequipoption .@from_slot, 1, @enchant1_type, @enchant1_value;
	}
	if (@enchant2_type) {
		equip2 .@item_id, 0, 0, .@card1, .@card2, .@card3, .@card4;
		setequipoption .@from_slot, 2, @enchant2_type, @enchant2_value;
	}
	if (@enchant3_type) {
		equip2 .@item_id, 0, 0, .@card1, .@card2, .@card3, .@card4;
		setequipoption .@from_slot, 3, @enchant3_type, @enchant3_value;
	}
	if (@enchant4_type) {
		equip2 .@item_id, 0, 0, .@card1, .@card2, .@card3, .@card4;
		setequipoption .@from_slot, 4, @enchant4_type, @enchant4_value;
	}
	if (@enchant5_type) {
		equip2 .@item_id, 0, 0, .@card1, .@card2, .@card3, .@card4;
		setequipoption .@from_slot, 5, @enchant5_type, @enchant5_value;
	}

	// Stash the refine level
	set stashed_refine, .@refine_level;
	set stashed_item_id, .@item_id;
	set stashed_item_loc, .@from_slot;
	if ($testserver == 1) {
		dispbottom "Debug: Transferring refine";
		dispbottom "Debug: from_slot = " + .@from_slot;
		dispbottom "Debug: item_id = " + .@item_id;
		dispbottom "Debug: refine_level = " + .@refine_level;
		dispbottom "Debug: stashed_item_loc = " + stashed_item_loc;
	}
	mes " ";
	mes "^008000Refine level stashed successfully!^000000";
	enable_items;
	close;

TransferEnchantOrb:
	next;
	check_bound_rental();
	mes "[ ^996600Refine Transfer^000000 ]";
	mes "-------------------------------";
	mes "Select the item to transfer the enchant orb from:";

	setarray .@position$[1], "Top", "Armor", "Left hand", "Right hand", "Garment", "Shoes", "Accessory 1", "Accessory 2", "Head Mid", "Head Low", "Costume Head Low", "Costume Head Mid", "Costume Head Top", "Costume Garment", "Shadow Armor", "Shadow Weapon", "Shadow Shield", "Shadow Shoes", "Shadow Accessory R", "Shadow Accessory L";
	.@menu$ = "";
	for (.@i = 1; .@i <= 20; ++.@i) {
		if (getequipisequiped(.@i)) {
			.@menu$ += .@position$[.@i] + " - [" + getequipname(.@i) + "]";
			.@equipped = 1;
		}
		.@menu$ += ":";
	}
	if (.@equipped == 0) {
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "I don't think I can transfer any enchant orbs from the items you have equipped.";
		close;
	}
	.@part = select(.@menu$);
	set .@from_slot, .@part;
	if (!check_whitelist(.@from_slot)) end;
	check_bound_rental();
	set .@orb_id, getequipcardid(.@from_slot, 3); // 4th slot is index 3
	if (.@orb_id == 0) {
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "This item has no enchant orb.";
		close;
	}
	//mes "The transfer will cost " + .Enchant_Zeny_Fee + " Zeny and " + .Enchant_Item_Amount + " Enchant Transfer Scroll(s).";
	if (Zeny < .Enchant_Zeny_Fee || countitem(.Enchant_Item_Fee) < .Enchant_Item_Amount) {
		next;
		mes "[ ^996600Refine Transfer^000000 ]";
		mes "-------------------------------";
		mes "You do not have enough Zeny or the required item.";
		mes " ";
		mes "Zeny: " + Zeny + " / " + .Enchant_Zeny_Fee;
		mes "Transfer Scroll: " + countitem(.Enchant_Item_Fee) + " / " + .Enchant_Item_Amount;
		close;
	}
	Zeny -= .Enchant_Zeny_Fee;
	delitem .Enchant_Item_Fee, .Enchant_Item_Amount;

	// Save item data before deletion
	set .@item_id, getequipid(.@from_slot);
	set .@card1, getequipcardid(.@from_slot, 0);
	set .@card2, getequipcardid(.@from_slot, 1);
	set .@card3, getequipcardid(.@from_slot, 2);
	set .@card4, getequipcardid(.@from_slot, 3);

	// save options
	@enchant1_type = getequipoption(.@from_slot,1,IT_OPT_INDEX);
	@enchant1_value = getequipoption(.@from_slot,1,IT_OPT_VALUE);
	@enchant2_type = getequipoption(.@from_slot,2,IT_OPT_INDEX);
	@enchant2_value = getequipoption(.@from_slot,2,IT_OPT_VALUE);
	@enchant3_type = getequipoption(.@from_slot,3,IT_OPT_INDEX);
	@enchant3_value = getequipoption(.@from_slot,3,IT_OPT_VALUE);
	@enchant4_type = getequipoption(.@from_slot,4,IT_OPT_INDEX);
	@enchant4_value = getequipoption(.@from_slot,4,IT_OPT_VALUE);
	@enchant5_type = getequipoption(.@from_slot,5,IT_OPT_INDEX);
	@enchant5_value = getequipoption(.@from_slot,5,IT_OPT_VALUE);

	// save refine
	@refine = getequiprefinerycnt(.@from_slot);

	// Remove enchant orb from the origin item
	delequip(.@from_slot);
	getitem2 .@item_id, 1, 1, @refine, 0, .@card1, .@card2, .@card3, 0; // Remove orb from 4th slot

	// Re apply options if any
	if (@enchant1_type) {
		equip2 .@item_id, @refine, 0, .@card1, .@card2, .@card3, 0;
		setequipoption .@from_slot, 1, @enchant1_type, @enchant1_value;
	}
	if (@enchant2_type) {
		equip2 .@item_id, @refine, 0, .@card1, .@card2, .@card3, 0;
		setequipoption .@from_slot, 2, @enchant2_type, @enchant2_value;
	}
	if (@enchant3_type) {
		equip2 .@item_id, @refine, 0, .@card1, .@card2, .@card3, 0;
		setequipoption .@from_slot, 3, @enchant3_type, @enchant3_value;
	}
	if (@enchant4_type) {
		equip2 .@item_id, @refine, 0, .@card1, .@card2, .@card3, 0;
		setequipoption .@from_slot, 4, @enchant4_type, @enchant4_value;
	}
	if (@enchant5_type) {
		equip2 .@item_id, @refine, 0, .@card1, .@card2, .@card3, 0;
		setequipoption .@from_slot, 5, @enchant5_type, @enchant5_value;
	}

	// Stash the enchant orb
	set stashed_orb_id, .@orb_id;
	set stashed_item_id, .@item_id;
	set stashed_item_loc, .@from_slot;
	if ($testserver == 1) {
		dispbottom "Debug: Transferring enchant orb";
		dispbottom "Debug: from_slot = " + .@from_slot;
		dispbottom "Debug: item_id = " + .@item_id;
		dispbottom "Debug: orb_id = " + .@orb_id;
		dispbottom "Debug: stashed_item_loc = " + stashed_item_loc;
	}
	mes " ";
	mes "^008000Orbs stashed successfully!^000000";
	enable_items;
	close;

DisplayWhitelist:
    .@page = 1;
    .@items_per_page = 20;
    .@search$ = "";

	freeloop(1);
    while (1) {
        next;
        mes "[ ^996600Refine Transfer^000000 ]";
        if (.@search$ == "") {
            mes "The following items are eligible for transfer:";
        } else {
            mes "Search results for: ^777777" + .@search$ + "^000000";
        }
        mes "-------------------------------";

        .@matching_items = 0;
        for (.@i = 0; .@i < getarraysize(.whitelist); .@i++) {
            .@item_name$ = getitemname(.whitelist[.@i]);
            if (.@search$ == "" || compare(.@item_name$, .@search$)) {
                if (.@matching_items >= (.@page - 1) * .@items_per_page && .@matching_items < .@page * .@items_per_page) {
                    mes " > ^777777" + .@item_name$ + "^000000";
                }
                .@matching_items++;
            }
        }

        .@total_pages = (.@matching_items + .@items_per_page - 1) / .@items_per_page;
        if (.@matching_items == 0) {
            mes "No items found.";
        }

        // Ensure consistent menu structure
        .@menu$ = "";
        .@menu$ += "[-] Next Page:";   // Always present for consistency
        .@menu$ += "[-] Previous Page:"; // Always present for consistency
        .@menu$ += "[-] Search Item:";
        .@menu$ += "[-] Close";

        switch (select(.@menu$)) {
            case 1:
                if (.@page < .@total_pages) {
                    .@page++;
                } else {
                    mes "This is the last page.";
                    next;
                }
                break;
            case 2:
                if (.@page > 1) {
                    .@page--;
                } else {
                    mes "This is the first page.";
                    next;
                }
                break;
            case 3:
				next;
                mes "[ ^996600Refine Transfer^000000 ]";
                mes "Please enter a keyword to search:";
                input .@search$; // Prompt user for search term
                if (.@search$ == "") {
                    mes "Search cleared.";
                } else {
                    mes "Searching for: ^777777" + .@search$ + "^000000";
                }
                .@page = 1; // Reset to first page after new search
                next;
                break;
            case 4:
                close;
        }
    }
	freeloop(0);
	enable_items;
	close;

}