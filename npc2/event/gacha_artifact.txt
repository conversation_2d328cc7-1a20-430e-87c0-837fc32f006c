prontera,164,75,5	script	Artifact Vending Machine	2_SLOT_MACHINE,{

	mes "[^996600 Artifact Gacha ^000000]";
	mes "Hello Adventurer!";
	mes "-------------------------------";
	mes "Welcome to Artifact Vending Machine! I can give you a fair chance on each artifact if you like to bet. Sounds good ya?";
	next;
	mes "[^996600 Artifact Gacha ^000000]";
	mes "WARNING!";
	mes "-------------------------------";
	mes "There's a chance to fail on each roll. And every time you failed, you will lose the Artifact Ticket.";	
	mes " ";
	mes "Do you still want to continue?";
	next;
	mes "[^996600 Artifact Gacha ^000000]";
	mes "-------------------------------";
	mes "What would you like to do?";
	menu "Play Gacha",<PERSON>_<PERSON>ach<PERSON>,"<PERSON><PERSON> Artifact",<PERSON>_<PERSON><PERSON>;
	
L_Gacha:
	next;
	mes "[^996600 Artifact Gacha ^000000]";
	mes "-------------------------------";
	mes "Pay me:";
	
	for (.@i =0;.@i<getarraysize(.FeeItem);.@i++) {
	    mes	"^008800"+.FeeAmt[.@i]+" "+getitemname(.FeeItem[.@i])+"^000000";
	}
	mes "and ^0088001 Activity Point^000000";
    mes "to play the game.";
	mes " ";
	mes "You'll get a chance to win these exclusive prizes!";
	next;
	mes "[^996600 Artifact Gacha ^000000]";
	mes "Weapon Name | Chance";
	mes "-------------------------------";
	for(set .@i,0; .@i < getarraysize(.ItemID); .@i++) {
		mes "> <ITEM>"+getitemname(.ItemID[.@i])+"<INFO>"+.ItemID[.@i]+"</INFO></ITEM> | ^88000060%^000000";
	}
	next;
	mes "[^996600 Artifact Gacha ^000000]";
	mes "-------------------------------";
	mes "So, master...";
	mes "Do you wanna gamble?";
	menu "Game on!",-;
	next;
	mes "[^996600 Artifact Gacha ^000000]";
	if(countitem(.FeeItem) < .FeeAmt || #ACTIVITYPOINTS < 1) {
		mes "-------------------------------";
		mes "Are you dumb? 'Coz I ain't!";
		close;
	}
	#ACTIVITYPOINTS--;
    for (.@i =0;.@i<getarraysize(.FeeItem);.@i++) {
        if (countitem(.FeeItem[.@i]) <.FeeAmt[.@i]) {
         mes "You are missing a required item";
         close;
        }
	}
    for (.@i =0;.@i<getarraysize(.FeeItem);.@i++) {
        delitem .FeeItem[.@i],.FeeAmt[.@i];
	}
	set .@Chance,rand(1,100);
	setarray .@ItemRwdList[0],0;
	setarray .@ItemRwdListAmt[0],0;
	for(set .@i,0; .@i < getarraysize(.ItemID); .@i++) {
		if(.ItemChance[.@i] >= .@Chance) {
			setarray .@ItemRwdList[getarraysize(.@ItemRwdList)],.ItemID[.@i];
			setarray .@ItemRwdListAmt[getarraysize(.@ItemRwdListAmt)],.ItemAmt[.@i];
			setarray .@ItemRwdListChance[getarraysize(.@ItemRwdListChance)],.ItemChance[.@i];
		}
	}

	set .@RwdSelector,getarraysize(.@ItemRwdList);
	set .@RwdChance,rand(.@RwdSelector);
	if(.@RwdSelector != 0) {
		getitem .@ItemRwdList[.@RwdChance],.@ItemRwdListAmt[.@RwdChance];
		if(.@ItemRwdListChance[.@RwdChance] <= 60) 
			announce strcharinfo(0)+" got "+getitemname(.@ItemRwdList[.@RwdChance])+" x"+.@ItemRwdListAmt[.@RwdChance]+" from Gacha - Artifact Vending Machine!",0;
			specialeffect2 154;
	}
	else { 
		dispbottom "You won nothing from "+strnpcinfo(1)+".";
		specialeffect2 155;
	}
	mes "-------------------------------";
	mes "Shssihasshiw...!";
	mes "Let's gamble again sometime!";
	close;

L_Reroll:
	next;
	mes "[^996600 Artifact Gacha ^000000]";
	mes "-------------------------------";
	mes "To reroll an artifact, you need:";
	mes "1 piece of any Artifact from:";
	for(set .@i,0; .@i < getarraysize(.ItemID); .@i++) {
		mes "- "+getitemname(.ItemID[.@i]);
	}
	mes "- 30x "+getitemname(3134);
	mes "- 300x "+getitemname(3100);
	next;
	mes "[^996600 Artifact Gacha ^000000]";
	mes "Do you want to proceed with rerolling?";
	menu "Yes, reroll",L_RerollConfirm,"No, cancel",-;
	close;

L_RerollConfirm:
	sleep2 1000;
	// Check for all required items
	set .@missing,0;
	set .@hasArtifact,0;
	mes " ";
	mes "Checking your inventory...";
	sleep2 1000;
	
	// Check if player has at least one artifact
	for(set .@i,0; .@i < getarraysize(.ItemID); .@i++) {
		if(countitem(.ItemID[.@i]) > 0) {
			set .@hasArtifact,1;
			set .@artifactID,.ItemID[.@i];
			break;
		}
	}
	if(!.@hasArtifact) {
		mes "Missing: Any artifact from the list";
		set .@missing,1;
	}
	
	if(countitem(3134) < 30) {
		mes "Missing: "+(30-countitem(3134))+"x "+getitemname(3134);
		set .@missing,1;
	}
	if(countitem(3100) < 300) {
		mes "Missing: "+(300-countitem(3100))+"x "+getitemname(3100);
		set .@missing,1;
	}
	if(.@missing) {
		next;
		mes "[^996600 Artifact Gacha ^000000]";
		mes "Please gather all required items first.";
		close;
	}
	
	// Remove one artifact and other required items
	delitem .@artifactID,1;
	delitem 3134,30;
	delitem 3100,300;
	
	// 60% chance of success
	set .@Chance,rand(1,100);
	if(.@Chance <= 60) {
		// Give random artifact
		set .@RwdSelector,getarraysize(.ItemID);
		set .@RwdChance,rand(.@RwdSelector);
		getitem .ItemID[.@RwdChance],1;
		announce strcharinfo(0)+" got "+getitemname(.ItemID[.@RwdChance])+" from Gacha - Artifact Vending Machine!",0;
		specialeffect2 154;
		mes "Reroll successful!";
		mes "Come back anytime!";
	} else {
		// Return the artifact on failure
		getitem .@artifactID,1;
		dispbottom "You failed to reroll from "+strnpcinfo(1)+".";
		specialeffect2 155;
		mes "Reroll failed!";
		mes "Your artifact has been returned.";
		mes "Better luck next time!";
	}
	close;

	OnInit:
		setarray .ItemID[0],44500,44501,44502,44503,44504,44505,44506,44507,44508,44509,44510,44511,44512,44513,44514,44515; //Set item ID for reward
		setarray .ItemAmt[0],1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1; //Set amount of item for reward
		setarray .ItemChance[0],50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50;
		setarray .FeeItem[0],44154;
		setarray .FeeAmt[0],1;
		end;
		
	OnPCLoadMapEvent:
		if(strcharinfo(3) == strnpcinfo(4)) {
			.@questInProgress = 1;
			showevent 0,0;
			showevent(.@questInProgress ? QTYPE_EVENT2 : QTYPE_NONE);
		}
	end;

}