-	script	Void Event	-1,{

OnInit:
	bindatcmd("joinvoid", strnpcinfo(NPC_NAME)+"::OnJoin",0,99);
	bindatcmd("startvoid", strnpcinfo(NPC_NAME)+"::OnStart",99,99);
	bindatcmd("endvoid", strnpcinfo(NPC_NAME)+"::OnEnd",99,99);
	bindatcmd("voidstatus", strnpcinfo(NPC_NAME)+"::OnVoidStatus",0,99);
	bindatcmd("void_noreward", strnpcinfo(NPC_NAME)+"::OnNoReward",99,99);
	bindatcmd("void_reward", strnpcinfo(NPC_NAME)+"::OnReward",99,99);
	bindatcmd("void_norewarduid", strnpcinfo(NPC_NAME)+"::OnNoRewardUID",99,99);
	bindatcmd("void_rewarduid", strnpcinfo(NPC_NAME)+"::OnRewardUID",99,99);
	bindatcmd("void_norewardip", strnpcinfo(NPC_NAME)+"::OnNoRewardIP",99,99);
	bindatcmd("void_rewardip", strnpcinfo(NPC_NAME)+"::OnRewardIP",99,99);
	bindatcmd("void_blocklist", strnpcinfo(NPC_NAME)+"::OnBlockList",99,99);
	bindatcmd("void_unblockall", strnpcinfo(NPC_NAME)+"::OnUnblockAll",99,99);
	bindatcmd("void_testblock", strnpcinfo(NPC_NAME)+"::OnTestBlock",99,99);
	bindatcmd("void_norewardacc", strnpcinfo(NPC_NAME)+"::OnNoRewardAcc",99,99);
	bindatcmd("void_rewardacc", strnpcinfo(NPC_NAME)+"::OnRewardAcc",99,99);
	.duration_minutes = 60*3;
	.multiplier = 1; // x30 increase hp each wave
	.multiplieratkmatk = 100;
	setarray(.mvps[0],3635,3636,3638,3637,3639,3640,3641,3642,3643,3644,3645,3646,3647,3654,3655,3656,3669,3670,3671,3672);
	setarray(.maps$[0],"payon","morocc","geffen","izlude","aldebaran","alberta","amatsu","yuno","comodo","gonryun","ayothaya","lighthalzen","hugel","rachel","einbech","brasilis","xmas","manuk","jawaii","umbala");
	setarray(.rewards[0],3875,1,100,3876,1,100,3134,3,100);
	setarray(.rewards_mvp[0],4458,4459,4461,4460,4462,4463,4464,4465,4466,4467,4468,4469,4470,4471,4472,4473,4518,4519,4520,4521);
	setarray(.rewards_amount[0],1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1);
	setarray(.rewards_chance[0],1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1);
	end;

OnTestBlock:
	.@unique$ = getcharip(getcharid(3));
	.@account = getcharid(3);
	.@isblocked = 0;
	for (.@b = 0;.@b < getarraysize($voidipblocklist$);.@b++) {
		if ($voidipblocklist$[.@b] == .@unique$) {
			.@isblocked = 1;
		}
	}
	for (.@b = 0;.@b < getarraysize($voidaccblocklist);.@b++) {
		if ($voidaccblocklist[.@b] == .@account) {
			.@isblocked = 1;
		}
	}
	if (#VOIDNOREWARD != 1 && .@isblocked != 1 && gepard_int_get("VOIDREWARDBLOCK") != 1) {
		dispbottom "You are not blocked.";
	} else {
		dispbottom "You are blocked.";
	}
	end;

OnBlockList:
	.@nb = query_sql("SELECT `account_id` FROM `acc_reg_num_db` WHERE `key`='#VOIDNOREWARD' AND `value`=1",.@blocklist);

	if (.@nb < 1) {
		dispbottom "No records found";
		end;
	}
	mes "[VOID Block list Acc]";
	mes "---------------------------------";
	for (.@i = 1; .@i < .@nb; .@i++) {
		mes .@blocklist[.@i];
	}
	mes "---------------------------------";
	mes "[VOID Block list UID]";
	.@nb = query_sql("SELECT `unique_id` FROM `gepard_reg_num` WHERE `key`='VOIDREWARDBLOCK' AND `value`=1",.@unique$);
	mes "---------------------------------";
	dispbottom "Total: "+.@nb;
	if (.@nb > 0) {
		
		for (.@i = 0; .@i < .@nb; .@i++) {
			mes .@unique$[.@i];
		}
		
	}
	mes "---------------------------------";
	mes "[VOID Block list IP]";
	mes "---------------------------------";
	for (.@i = 0; .@i < getarraysize($voidipblocklist$); .@i++) {
		mes $voidipblocklist$[.@i];
	}
	mes "---------------------------------";
	mes "[VOID Block list Account ID]";
	mes "---------------------------------";
	for (.@i = 0; .@i < getarraysize($voidaccblocklist); .@i++) {
		mes $voidaccblocklist[.@i];
	}
	mes "---------------------------------";
	close;
	end;

OnNoRewardUID:
	// block player based on last_unique_id in login table
	if (.@atcmd_numparameters != 1) {
		dispbottom "Usage: @void_norewarduid <account_id>";
		end;
	}
	.@account = .@atcmd_parameters$;
	.@nb = query_sql("SELECT `last_unique_id` FROM `login` WHERE `account_id`='"+.@account+"'",.@uid$);
	if (.@nb < 1) {
		dispbottom "Account not found";
		end;
	}

	gepard_int_set("VOIDREWARDBLOCK",1,0,.@uid$[0]);

	dispbottom "Account "+.@account+" with unique id of "+.@uid$+" blocked from receiving rewards";
	end;

OnRewardUID:
	// unblock uid
	if (.@atcmd_numparameters != 1) {
		dispbottom "Usage: @void_rewarduid <account_id>";
		end;
	}
	.@account = .@atcmd_parameters$;
	.@nb = query_sql("SELECT `last_unique_id` FROM `login` WHERE `account_id`='"+.@account+"'",.@uid$);
	if (.@nb < 1) {
		dispbottom "Account not found";
		end;
	}

	gepard_int_set("VOIDREWARDBLOCK",0,0,.@uid$[0]);

	dispbottom "Account "+.@account+" with unique id of "+.@uid$+" allowed from receiving rewards";
	end;

OnNoRewardIP:
	// block player based on last_ip in login table
	if (.@atcmd_numparameters != 1) {
		dispbottom "Usage: @void_norewardip <account_id>";
		end;
	}
	.@account = .@atcmd_parameters$;
	.@nb = query_sql("SELECT `last_ip` FROM `login` WHERE `account_id`='"+.@account+"'",.@ip$);
	if (.@nb < 1) {
		dispbottom "Account not found";
		end;
	}

	setarray($voidipblocklist$[getarraysize($voidipblocklist$)],.@ip$[0]);

	dispbottom "Account "+.@account+" with ip of "+.@ip$+" blocked from receiving rewards";
	end;

OnRewardIP:
	// unblock ip
	if (.@atcmd_numparameters != 1) {
		dispbottom "Usage: @void_rewardip <account_id>";
		end;
	}
	.@account = .@atcmd_parameters$;
	.@nb = query_sql("SELECT `last_ip` FROM `login` WHERE `account_id`='"+.@account+"'",.@ip$);
	if (.@nb < 1) {
		dispbottom "Account not found";
		end;
	}

	for (.@i = 0; .@i < getarraysize($voidipblocklist$); .@i++) {
		if ($voidipblocklist$[.@i] == .@ip$[0]) {
			deletearray $voidipblocklist$[.@i],1;
			break;
		}
	}

	dispbottom "Account "+.@account+" with ip of "+.@ip$+" allowed from receiving rewards";
	end;

OnNoRewardAcc:
	// block player based on account_id
	if (.@atcmd_numparameters != 1) {
		dispbottom "Usage: @void_norewardacc <account_id>";
		end;
	}
	.@account = .@atcmd_parameters$;
	setarray($voidaccblocklist[getarraysize($voidaccblocklist)],.@account);
	dispbottom "Account "+.@account+" blocked from receiving rewards";
	end;

OnRewardAcc:
	// unblock account_id
	if (.@atcmd_numparameters != 1) {
		dispbottom "Usage: @void_rewardacc <account_id>";
		end;
	}
	.@account = .@atcmd_parameters$;
	for (.@i = 0; .@i < getarraysize($voidaccblocklist); .@i++) {
		if ($voidaccblocklist[.@i] == .@account) {
			deletearray $voidaccblocklist[.@i],1;
			break;
		}
	}
	dispbottom "Account "+.@account+" allowed from receiving rewards";
	end;

OnNoReward:
	if (.@atcmd_numparameters != 1) {
		dispbottom "Usage: @void_noreward <account_id>";
		end;
	}
	.@account = .@atcmd_parameters$;
	
	if (isloggedin(.@account)) {
		.@orig = getcharid(CHAR_ID_ACCOUNT);
		attachrid(.@account);
		#VOIDNOREWARD = 1;
	}
	query_sql("INSERT INTO `acc_reg_num_db` (`account_id`,`key`,`value`) VALUES ('"+.@account+"','#VOIDNOREWARD','1') ON DUPLICATE KEY UPDATE `value` = '1'");

	attachrid(.@orig);
	dispbottom .@account+" has been blocked from receiving void reward.";
	end;

OnReward:
	if (.@atcmd_numparameters != 1) {
		dispbottom "Usage: @void_reward <account_id>";
		end;
	}
	.@account = .@atcmd_parameters$;
	
	if (isloggedin(.@account)) {
		.@orig = getcharid(CHAR_ID_ACCOUNT);
		attachrid(.@account);
		#VOIDNOREWARD = 0;
	}
	query_sql("INSERT INTO `acc_reg_num_db` (`account_id`,`key`,`value`) VALUES ('"+.@account+"','#VOIDNOREWARD','0') ON DUPLICATE KEY UPDATE `value` = '0'");

	attachrid(.@orig);
	dispbottom .@account+" has been allowed to receive void reward.";
	end;


OnUnblockAll:
	.@nb = query_sql("SELECT `account_id` FROM `acc_reg_num_db` WHERE `key`='#VOIDNOREWARD' AND `value`=1",.@blocklist);

	if (.@nb < 1) {
		dispbottom "No records found";
		end;
	}
	dispbottom "[VOID Block list]";
	dispbottom "---------------------------------";
	for (.@i = 1; .@i < .@nb; .@i++) {
		dispbottom .@blocklist[.@i] + " has been unblocked.";
		.@account = .@blocklist[.@i];
		if (isloggedin(.@account)) {
			.@orig = getcharid(CHAR_ID_ACCOUNT);
			attachrid(.@account);
			#VOIDNOREWARD = 0;
		}
		query_sql("INSERT INTO `acc_reg_num_db` (`account_id`,`key`,`value`) VALUES ('"+.@account+"','#VOIDNOREWARD','0') ON DUPLICATE KEY UPDATE `value` = '0'");

		attachrid(.@orig);
	}
	// unblock all unique_id
	query_sql("DELETE FROM `gepard_reg_num` WHERE `key`='VOIDREWARDBLOCK' AND `value`=1");
	deletearray($voidipblocklist$);
	deletearray($voidaccblocklist);
	dispbottom "---------------------------------";
	end;


OnVoidStatus:
	dispbottom("=========================================================",0xFFC65B);
	dispbottom("                                        [ Void Event --- MVP Status ]",0xFFC65B);
	dispbottom("=========================================================",0xFFC65B);
	if (getd("."+.mvpid+"mobstatus"))
		dispbottom("[ Void MVP ]: "+getmonsterinfo(.mvpid, MOB_NAME)+" is still alive at "+.map$+".",0xFFFF69);
	else 
		dispbottom("Please wait for next void appearance...",0xFFFF69);
	end;

OnJoin:
	getmapxy .@map$,.@x,.@y,0;
	if (getmapflag(.@map$,mf_gvg_castle) || getmapflag(.@map$,mf_nowarp) || getmapflag(.@map$,mf_gvg_castle) || getmapflag(.@map$,mf_gvg_dungeon) || getmapflag(.@map$,mf_nowarpto) || getmapflag(.@map$,mf_nowarp)) {
		dispbottom "You cannot use this feature at this map...";
		end;
	}
	//for (.@i = 0;.@i < getarraysize(.mvps);.@i++) {
	//	setd("void"+@mvpid,0);
	//}
	warp .map$,0,0;
	end;

OnEnd:
	.minutes = 0;
	//mapwarp(.map$, "prontera", 160, 180);
	announce "[ Void Event ] Void has ended",bc_all|bc_blue;
	end;


//OnClock00:
OnWed1030:
OnSat1130:
OnStart:
	announce "[ Void Event ] Void Event will start in 2 minutes.",bc_all|bc_blue;
	//announce "[ Void Event ] Void Event with '3% MVP Card drop rate' will start in 2 minutes.",bc_all|bc_blue;
	if (!$testvoid) sleep(60*1000);
	announce "[ Void Event ] Void will start in 1 minute.",bc_all|bc_blue;
	if (!$testvoid) sleep(30*1000);
	announce "[ Void Event ] Void will start in 30 seconds.!",bc_all|bc_blue;
	sleep(20*1000);
	announce "[ Void Event ] Void will start in 10 seconds.",bc_all|bc_blue;
	sleep(10*1000);
	announce "[ Void Event ] Void has started.",bc_all|bc_blue;
	

	announce "[ Void Event ] The Void will be summoning MVP Monsters every 15 minutes for "+(.duration_minutes/60)+" hours.",bc_all|bc_blue;
	sleep(5*1000);
	announce "[ Void Event ] A random attacker will be rewarded each time an MVP is killed.",bc_all|bc_blue;
	sleep(5*1000);
	announce "[ Void Event ] You may use @voidstatus to check if a void MVP is alive.",bc_all|bc_blue; 
	sleep(5*1000);
	//announce "[ Void Event ] First MVP will arrive in 10",bc_all|bc_blue; 
	sleep(1*1000);
	//announce "[ Void Event ] First MVP will arrive in 9",bc_all|bc_blue; 
	sleep(1*1000);
	//announce "[ Void Event ] First MVP will arrive in 8",bc_all|bc_blue; 
	sleep(1*1000);
	//announce "[ Void Event ] First MVP will arrive in 7",bc_all|bc_blue; 
	sleep(1*1000);
	//announce "[ Void Event ] First MVP will arrive in 6",bc_all|bc_blue; 
	sleep(1*1000);

	.minutes = .duration_minutes;
	.@wave = 1;
	while(.minutes > 0) {
		if (.minutes % 15 == 0) {
			announce "[ Void Event ] MVP will arrive in 5",bc_all|bc_blue; 
			sleep(1*1000);
			announce "[ Void Event ] MVP will arrive in 4",bc_all|bc_blue; 
			sleep(1*1000);
			announce "[ Void Event ] MVP will arrive in 3",bc_all|bc_blue; 
			sleep(1*1000);
			announce "[ Void Event ] MVP will arrive in 2",bc_all|bc_blue; 
			sleep(1*1000);
			announce "[ Void Event ] MVP will arrive in 1",bc_all|bc_blue; 
			sleep(1*1000);
			.@r = rand(0,getarraysize(.mvps)-1);
			
			.mvpid = .mvps[.@r];

			if ($testvoidmob)
				.mvpid = $testvoidmob; //3671;
			
			.map$ = .maps$[.@r];
			setmapflag .map$,mf_loadevent; 
			.@gid = monster(.map$, rand(0,0), rand(0,0), "Void", .mvpid, 1, strnpcinfo(NPC_NAME)+"::OnMvPDead");
			debugmes "[Void Beta] GID : "+.@gid;
			.@currenthp = getunitdata(.@gid,UDT_MAXHP);
			debugmes "[Void Beta] Current HP: "+.@currenthp;
			.@newhp = (.@currenthp*.multiplier);

			//if ($testvoid)
				//.@newhp = 10000;
	
			setunitdata(.@gid,UDT_MAXHP,.@newhp);
			setunitdata(.@gid,UDT_HP,.@newhp);

			setunitdata(.@gid,UDT_MAXHP2,.@newhp);
			setunitdata(.@gid,UDT_RESIST,50);



			.@newhp = getunitdata(.@gid,UDT_MAXHP);
			debugmes "[Void Beta] New HP: "+.@newhp;


			.@currentatkmin = getunitdata(.@gid,UDT_ATKMIN);
			.@newatkmin = (.@currentatkmin*(.multiplieratkmatk));
			.@currentatkmax = getunitdata(.@gid,UDT_ATKMAX);
			.@newatkmax = (.@currentatkmax*(.multiplieratkmatk));

			

			.@currentmatkmin = getunitdata(.@gid,UDT_ATKMIN);
			.@newmatkmin = (.@currentmatkmin*(.multiplieratkmatk));
			.@currentmatkmax = getunitdata(.@gid,UDT_ATKMAX);
			.@newmatkmax = (.@currentmatkmax*(.multiplieratkmatk));

			if (.@newmatkmin > 65000)
				.@newmatkmin = 65000;
			if (.@newmatkmax > 65000)
				.@newmatkmax = 65000;
			if (.@newatkmin > 65000)
				.@newatkmin = 65000;
			if (.@newatkmax > 65000)
				.@newatkmax = 65000;

			setunitdata(.@gid,UDT_ATKMIN,.@newatkmin);
			setunitdata(.@gid,UDT_ATKMAX,.@newatkmax);
			setunitdata(.@gid,UDT_MATKMIN,.@newmatkmin);
			setunitdata(.@gid,UDT_MATKMAX,.@newmatkmax);

			set getd("."+.mvpid+"mobstatus"),1;
			announce "[ Void Event ] Void MVP Appeared at "+.map$+" type @joinvoid to participate.",bc_all|bc_blue; 
			if ($testvoid)
				announce "[Debug] Mob id: "+.mvpid,0;
			.@wave++;
		}
		.minutes--;
		sleep(60000);
	}
	callsub(OnEnd);
	end;


OnMvPDead:
	getmapxy(.@map$, .@mapx, .@mapy, 0);
	freeloop(1);
	
	.@mvpid = killedrid;
	set getd("."+.mvpid+"mobstatus"),0;
	deletearray(.@units);
	.@count = getunits(BL_PC, .@units, 100, .@map$);

	.@p = 0;
	.@i = 0;

	/*
gepard_int_get(<"key">,{<index>{,<"unique id">}});
gepard_string_get(<"key">,{<index>{,<"unique id">}});
gepard_int_set(<"key">,<value>{,<index>{,<"unique id">}});
gepard_string_set(<"key">,<"value">{,<index>{,<"unique id">}});

*/

	while (.@p <= .@count) {
		if (.@units[.@p] && (playerattached() == .@units[.@p] || attachrid(.@units[.@p]))) {
			.@unique$ = getcharip(getcharid(3));
			.@account = getcharid(3);
			.@isblocked = 0;
			for (.@b = 0;.@b < getarraysize($voidipblocklist$);.@b++) {
				if ($voidipblocklist$[.@b] == .@unique$) {
					.@isblocked = 1;
				}
			}
			for (.@b = 0;.@b < getarraysize($voidaccblocklist);.@b++) {
				if ($voidaccblocklist[.@b] == .@account) {
					.@isblocked = 1;
				}
			}

			if ($testvoid) {
				dispbottom "IP blocked: "+.@isblocked;
				dispbottom "Gepard Blocked: "+gepard_int_get("VOIDREWARDBLOCK");
			}
			if (#VOIDNOREWARD != 1 && .@isblocked != 1 && gepard_int_get("VOIDREWARDBLOCK") != 1 && gettimetick(2)-getd("void"+.@mvpid) < 30 && voidgotrewardn < gettimetick(2) && gepard_int_get("VOIDREWARD") < gettimetick(2)) {
				voidgotrewardn = gettimetick(2)+10;
				gepard_int_set("VOIDREWARD",gettimetick(2)+10);
				while (.@i < getarraysize(.rewards)) {
					if (rand(1,100) <= .rewards[.@i+2]) {	
						getitem .rewards[.@i],.rewards[.@i+1];
						announce "[ Void Event ] "+strcharinfo(0)+" obtained "+(.rewards[.@i+1])+"x "+getitemname(.rewards[.@i]),bc_all|bc_blue; 

						if ( (.rewards[.@i] == 3875 || .rewards[.@i] == 3876)) {
							@blockvoidreward = 1;
						}

					}
					.@i+=3;
					break;
				}

				if (.@i >= getarraysize(.rewards)) {
					break;
				}
			}
		}
		//announce "tried "+.@units[.@p],0;
		.@p++;
	}


	.@p = 0;
	
	.@b = 0;
	while (.@p < .@count) {
		if (.@units[.@p] && (playerattached() == .@units[.@p] || attachrid(.@units[.@p]))) {
			.@unique$ = getcharip(getcharid(3));
			.@account = getcharid(3);
			.@isblocked = 0;
			for (.@b = 0;.@b < getarraysize($voidipblocklist$);.@b++) {
				if ($voidipblocklist$[.@b] == .@unique$) {
					.@isblocked = 1;
				}
			}
			for (.@b = 0;.@b < getarraysize($voidaccblocklist);.@b++) {
				if ($voidaccblocklist[.@b] == .@account) {
					.@isblocked = 1;
				}
			}
			
			if ($testvoid) {
				dispbottom "#VOIDNOREWARD : "+#VOIDNOREWARD;
				dispbottom "gepard_int_get(\"VOIDREWARDBLOCK\") : "+gepard_int_get("VOIDREWARDBLOCK");
				dispbottom "gettimetick(2)-getd(\"void\"+.@mvpid) : "+(gettimetick(2)-getd("void"+.@mvpid));
				dispbottom "voidgotreward : "+voidgotreward;
				dispbottom "gepard_int_get(\"VOIDREWARDMVP\") : "+gepard_int_get("VOIDREWARDMVP");
				dispbottom "isblocked : "+.@isblocked;
				dispbottom "gettimetick"+gettimetick(2);
			}

			if (#VOIDNOREWARD != 1 && .@isblocked != 1 && gepard_int_get("VOIDREWARDBLOCK") != 1  && (getd("void"+.@mvpid)-gettimetick(2)) < 30 && (voidgotreward < gettimetick(2)) && gepard_int_get("VOIDREWARDMVP") < gettimetick(2)) {
				
				
				
				voidgotreward = gettimetick(2)+10;
				gepard_int_set("VOIDREWARDMVP",gettimetick(2)+10);
				if ($testvoid) announce "[Debug] Trying to send mvp reward",0;

				.@i = 0;
				while (.@i < getarraysize(.rewards_mvp)) {
					if (.mvps[.@i] == .@mvpid && rand(1,100) <= .rewards_chance[.@i]) {
						getitem .rewards_mvp[.@i],.rewards_amount[.@i];
						announce "[ Void Event ] "+strcharinfo(0)+" obtained "+(.rewards_amount[.@i])+"x "+getitemname(.rewards_mvp[.@i]),bc_all|bc_blue; 
						if ($testvoid) announce "[Debug] MvP Reward Given",0;
						.@b = 1;
						break;
					}
					.@i++;		
				}
			} else {
				if ($testvoid) announce "[Debug] mvp reward blocked "+.@units[.@p],0;
			}
		} else {
			if ($testvoid) announce "[Debug] Unable to attach "+.@units[.@p],0;
		}
		if (.@b > 0)
			break;
		.@p++;
	}

	
	.@p = 0;
	while (.@p < .@count) {
		if (.@units[.@p] && (playerattached() == .@units[.@p] || attachrid(.@units[.@p]))) {
			if (!@blockvoidreward)  { .@p++; continue; }

			@blockvoidreward = 0;
			.@account = getcharid(3);
			deletearray(.@uid$);
			deletearray(.@ip$);
			.@nb = query_sql("SELECT `last_unique_id`,`last_ip` FROM `login` WHERE `account_id`='"+.@account+"'",.@uid$,.@ip$);
			if (.@nb) {
				gepard_int_set("VOIDREWARDBLOCK",1,0,.@uid$[0]);

				setarray($voidipblocklist$[getarraysize($voidipblocklist$)],.@ip$[0]);
				setarray($voidaccblocklist[getarraysize($voidaccblocklist)],.@account);

				if ($testvoid) {
					dispbottom "IP blocked: "+.@ip$[0];
					dispbottom "Gepard Blocked: "+gepard_int_get("VOIDREWARDBLOCK");
					dispbottom "Account blocked: "+.@account;
				}
			}

			
		}
		.@p++;
	}

	freeloop(0);
	sleep 10000;
	.map$ = "";
	end;

OnPCMvPDamage:
	if (strcharinfo(3) != .map$) end;
	setd("void"+@mvpid,gettimetick(2));
	end;

OnPCLoadMapEvent:
	.@g = 0;

	if (.map$ == strcharinfo(3)) {
		.@g = 1;
		.@limit = 1;
	}
	
	//if (getgmlevel() > 5) .@g = 0;
	if (!.@g) end;
	if (!get_unique_id()) {
		dispbottom "Error 1132 occured. Please take screenshot and report.";
		atcommand "@kick "+.@player$;
		end;
	}
	deletearray(.@account_id);
	.@nb = query_sql("SELECT account_id FROM `login` WHERE `last_unique_id` = "+get_unique_id(),.@account_id);

	.@count = 0;
	for (.@i = 0;.@i < .@nb;.@i++) {
		if (strcharinfo(3,"",.@account_id[.@i]) != "" && strcharinfo(3,"",.@account_id[.@i]) == strcharinfo(3)) {
			.@count++;
		}
	}

	if (.@count > .@limit) {
		dispbottom "You can't access this map at the moment. Client allowed in this map: "+.@limit;
		warp "prontera",156,170;
		end;
	}
	end;

}
