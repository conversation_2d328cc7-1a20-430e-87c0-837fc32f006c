prontera,209,130,4	script	Monthsary Giveaway	4_M_KID1,{
	
	switch(select("Monthly Giveaway:Monthly Free Job Change")) {
		case 1: break;
		case 2: doevent ("FreeJobChange::OnTalk"); end;
	}
	
OnTalk:
	mes .npcName$;
	mes "Happy Monthsary!";
	mes "-------------------------------";
	mes "Hello there, "+strcharinfo(0)+"!";
	mes "Would you like to claim your rewards?";
	menu "Maybe later..",-,"Yes please!",Lyes;
	close;
	
	Lyes:
		mes " ";
		getinventorylist();
		if(#Monthsary25 > 6) {
			mes "You can only claim this reward once per account!";
		} else if(Weight > (MaxWeight/2) || @inventorylist_count > 50) {
			mes "Please make sure that you have enough slots in your inventory and you can actually carry weight!";
			close;
		} else {
			mes "Here you go, thanks again!";
			#Monthsary25 = 7;

			getitembound 3250,50,1;
			getitembound 3121,50,1;
			getitembound 3122,50,1;
			getitembound 3123,50,1;
			getitembound 12210,3,1;
			getitembound 14545,3,1;
			getitembound 12103,10,1;
			getitembound 12247,10,1;
			getitembound 14159,3,1;
			getitembound 14160,3,1;
			getitembound 14161,3,1;
			getitembound 14162,3,1;
			getitembound 14163,3,1;
			getitembound 14164,3,1;
			getitembound 14601,3,1;
			getitembound 12424,3,1;
			getitembound 8206,1,1;
			getitembound 3157,1,1;
			//getitembound 20907,1,1; //HG bounded
			//getitembound 21130,1,1; //HG bounded
			rentitem 30069,604800;
			rentitem 30213,604800;
			rentitem 30213,604800;
			//rentitem callfunc("F_Rand",20118,20117,8958,8959),604800;
			dispbottom "Enjoy your free 7 Days VIP! You can now use @vipstatus and @vipbuffs command.";
			
			.@subdays = 7;
			if(gettimetick(2) < #vip_time) {
				set #vip_time, #vip_time + (86400 * .@subdays);
			} else {
				set #vip_time, gettimetick(2) + (86400 * .@subdays);
			}
		}
		close;

	OnInit:
		.npcName$ = "[^996600 "+strnpcinfo(1)+" ^000000]";
		waitingroom "Monthsary Giveaway!",0;
		end;

}