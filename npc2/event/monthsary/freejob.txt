-	script	FreeJobChange	-1,{
OnTalk:
	if (Class != Job_Novice && BaseLevel != 1) {
		mes "[ ^996600Free Job Changer^000000 ]";
		mes "-------------------------------";
		mes "I'm sorry, Adventurer,";
		mes "Only ^0000FFNovice Level 1^000000 can use this feature!";
		close;
	}
	if (#free3jc > 6) {
		mes "[ ^996600Free Job Changer^000000 ]";
		mes "-------------------------------";
		mes "You are only allowed to use this feature once a month and once per account.";
		close;
	}
	mes "[ ^996600Free Job Changer^000000 ]";
	mes "Hello!! "+strcharinfo(0)+"!";
	mes "-------------------------------";
	mes "Want an Instant Job Change?";
	mes "Please choose what class you want...";
	next;
	menu
	"<PERSON><PERSON> Knight",<PERSON><PERSON>,
	"<PERSON><PERSON>",<PERSON><PERSON>,
	"<PERSON>",<PERSON><PERSON><PERSON>,
	"<PERSON> Bishop",<PERSON>,
	"<PERSON><PERSON><PERSON>",<PERSON><PERSON><PERSON>,
	"Guillotine Cross",<PERSON><PERSON><PERSON>,
	"<PERSON> <PERSON>",<PERSON><PERSON>,
	"<PERSON><PERSON>",<PERSON><PERSON><PERSON>,
	"<PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON>",<PERSON><PERSON>w,
	"<PERSON><PERSON>",<PERSON><PERSON><PERSON>,
	"<PERSON><PERSON>",<PERSON><PERSON>,
	"<PERSON> <PERSON>r",<PERSON><PERSON><PERSON>,
	"<PERSON><PERSON><PERSON><PERSON><PERSON>",<PERSON><PERSON><PERSON><PERSON>,
	"<PERSON><PERSON><PERSON>",<PERSON><PERSON><PERSON>,
	"<PERSON><PERSON>",<PERSON><PERSON><PERSON>,
	"<PERSON><PERSON>",<PERSON><PERSON><PERSON>,
	"<PERSON><PERSON>",<PERSON><PERSON><PERSON>,
	"<PERSON>",<PERSON><PERSON><PERSON>,
	"<PERSON> <PERSON><PERSON>",<PERSON><PERSON>g,
	"Quit",Lcancel;

Lcancel:
	close;


Lrk:
jobchange Job_Rune_Knight_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lwl:
jobchange Job_Warlock_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lranger:
jobchange Job_Ranger_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lab:
jobchange Job_Arch_Bishop_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lmech:
jobchange Job_Mechanic_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lgx:
jobchange Job_Guillotine_Cross_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lrg:
jobchange Job_Royal_Guard_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lsorc:
jobchange Job_Sorcerer_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lmw:
if(Sex == 0)
jobchange Job_Wanderer_T;
else
jobchange Job_Minstrel_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;
close;

Lshura:
jobchange Job_Sura_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lgene:
jobchange Job_Genetic_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lsc:
jobchange Job_Shadow_Chaser_T;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lnecro:
jobchange 4233;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;
close;

Lkage:
jobchange 4234;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;
close;

Ljedi:
jobchange 4231;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;
close;

Lsith:
jobchange 4232;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;
close;

Lguns:
jobchange 24;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lninja:
jobchange 25;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

Lsg:
jobchange 4047;
set BaseLevel,255;
set JobLevel,120;
#free3jc = 7; close;

}