//============================================================
//===== Utility Scripts [A-Z] ================================
//============================================================
"npc2/utility/autoattack.txt",
"npc2/utility/deposit.txt",
"npc2/utility/nodoram.txt",
"npc2/utility/herc/atafk.txt",
//"npc2/utility/botcheck.txt",
"npc2/utility/backpack.txt",
"npc2/utility/broadcaster.txt",
"npc2/utility/chatdelay.txt",
"npc2/utility/creditexchange.txt",
"npc2/utility/creditexchange2.txt",
"npc2/utility/dotarunes.txt",
"npc2/utility/buildmanager.txt",
"npc2/utility/dismantle.txt",
"npc2/utility/goldroom.txt",
"npc2/utility/guildlimit.txt",
"npc2/utility/gpack.txt",
"npc2/utility/guildpack.txt",
"npc2/utility/healer.txt",
"npc2/utility/hourlybc.txt",
"npc2/utility/kafra.txt",
"npc2/utility/jailtime.txt",
"npc2/utility/mapflag.txt",
"npc2/utility/linker.txt",
"npc2/utility/mvp_arena.txt",
"npc2/utility/armorenchant.txt",
"npc2/utility/costumeenchant.txt",
"npc2/utility/infoboard.txt",
"npc2/utility/pvpwarper.txt",
"npc2/utility/reset.txt",
"npc2/utility/woe_rank.txt",
"npc2/utility/woe_timer.txt",
"npc2/utility/woe_reward.txt",
"npc2/utility/woe_mvp.txt",
"npc2/utility/roguemaster.txt",
"npc2/utility/costumeitem.txt",
"npc2/utility/emptest.txt",
"npc2/utility/punchingbag.txt",
"npc2/utility/herc/privatedbroom.txt",
"npc2/utility/herc/stylist.txt",
"npc2/utility/warper.txt",
"npc2/utility/guildrecruit.txt",
"npc2/utility/jobcostume.txt",
"npc2/utility/shop_mall.txt",
"npc2/utility/shop_donate.txt",
"npc2/utility/shop_actvote.txt",
"npc2/utility/shop_koewoe.txt",
"npc2/utility/shop_streamer.txt",
"npc2/utility/dota_pvpladder.txt",
"npc2/utility/bgvg_announce.txt",
"npc2/utility/playerlogin.txt",
"npc2/utility/changename.txt",
"npc2/utility/changegender.txt",
"npc2/utility/race.txt",
"npc2/utility/refinemaster.txt",
//"npc2/utility/auratrader.txt",
"npc2/utility/ticketexchange.txt",
"npc2/utility/vip.txt",
"npc2/utility/mvpspawn.txt",
"npc2/utility/dressingcoach.txt",
"npc2/utility/socketenchant.txt",
"npc2/utility/cardtrader.txt",
"npc2/utility/referral.txt",
"npc2/utility/referral_shop.txt",
"npc2/utility/enchantremover.txt",
"npc2/utility/refineremover.txt",
"npc2/utility/classmastery.txt",
"npc2/utility/options.txt",
"npc2/utility/homunpetlvlup.txt",
"npc2/utility/homunpetquest.txt",
"npc2/utility/fusion.txt",
"npc2/utility/channel.txt",
"npc2/utility/statue.txt",
"npc2/utility/charms.txt",
//============================================================
//===== Command Scripts [A-Z] ================================
//============================================================
"npc2/cmd/atsleep.txt",
"npc2/cmd/atbombring.txt",
"npc2/cmd/atalootid2.txt",
"npc2/cmd/atdice.txt",
"npc2/cmd/athold.txt",
"npc2/cmd/atcount.txt",
"npc2/cmd/atbank.txt",
"npc2/cmd/atbcstream.txt",
"npc2/cmd/atcommands.txt",
"npc2/cmd/atdonation.txt",
"npc2/cmd/athalter.txt",
"npc2/cmd/atevent.txt",
"npc2/cmd/atevents.txt",
"npc2/cmd/atreward.txt",
"npc2/cmd/atnpc.txt",
"npc2/cmd/atitemrain.txt",
"npc2/cmd/athatereset.txt",
"npc2/cmd/atii2.txt",
"npc2/cmd/atipcheck.txt",
"npc2/cmd/atlhz.txt",
"npc2/cmd/atguildbc.txt",
"npc2/cmd/atpc.txt",
"npc2/cmd/atpoints.txt",
"npc2/cmd/atrates.txt",
"npc2/cmd/atreloadnpc.txt",
"npc2/cmd/atrequest.txt",
"npc2/cmd/atresetchar.txt",
"npc2/cmd/atschedule.txt",
"npc2/cmd/atshortcut.txt",
"npc2/cmd/atsecurity.txt",
"npc2/cmd/attime.txt",
"npc2/cmd/atsanction.txt",
"npc2/cmd/atwoe.txt",
"npc2/cmd/atmac.txt",
"npc2/cmd/atwhomac.txt",
"npc2/cmd/athbc.txt",
"npc2/cmd/atiteminfo.txt",
"npc2/cmd/atgstorage.txt",
"npc2/cmd/atstorage.txt",
"npc2/cmd/atchatblock.txt",
"npc2/cmd/atsettings.txt",
"npc2/cmd/atdnd.txt",
"npc2/cmd/atnocutin.txt",
"npc2/cmd/atautopot.txt",
"npc2/cmd/atrestock.txt",
"npc2/cmd/atthq.txt",
//============================================================
//===== Quest Scripts [A-Z] ==================================
//============================================================
"npc2/quest/cdropexc.txt",
"npc2/quest/arena.txt",
"npc2/quest/breaktheseal.txt",
"npc2/quest/dailyquest.txt",
"npc2/quest/dailyreward.txt",
"npc2/quest/novice.txt",
"npc2/quest/shop_quest.txt",
"npc2/quest/shop_void.txt",
"npc2/quest/guildcontrib.txt",
"npc2/quest/huntingmission.txt",
"npc2/quest/brewer.txt",
//"npc2/quest/brewer2.txt",
//"npc2/quest/brewer3.txt",
"npc2/quest/jeweler.txt",
"npc2/quest/cosweap.txt",
"npc2/quest/aura.txt",
"npc2/quest/mining.txt",
"npc2/quest/boxtrader.txt",
"npc2/quest/dragweap.txt",
"npc2/quest/medal.txt",
"npc2/quest/hoh.txt",
"npc2/quest/avquest.txt",
//"npc2/quest/alcohol.txt",
"npc2/quest/tier.txt",
"npc2/quest/tier2.txt",
"npc2/quest/tier3.txt",
"npc2/quest/otherworld.txt",
"npc2/quest/shop_bts.txt",
"npc2/quest/missionboard.txt",
"npc2/quest/shop_card.txt",
"npc2/quest/fish.txt",
"npc2/quest/farm.txt",
"npc2/quest/farm_func.txt",
"npc2/quest/rebirth.txt",
//"npc2/quest/shop_tier.txt",

//"npc2/quest/questlog/feature_quest.txt",
//"npc2/quest/questlog/give_all_quest.txt",

//============================================================
//===== Mapflags [A-Z] ==================================
//============================================================
"npc2/mapflags/antidual.txt",
"npc2/mapflags/allowpk.txt",
"npc2/mapflags/allowks.txt",
"npc2/mapflags/nocommand.txt",
"npc2/mapflags/noicewall.txt",
"npc2/mapflags/noitem.txt",
"npc2/mapflags/noloot.txt",
"npc2/mapflags/noskill.txt",
"npc2/mapflags/noreturn.txt",
"npc2/mapflags/nosave.txt",
"npc2/mapflags/novending.txt",
"npc2/mapflags/nowarp.txt",
"npc2/mapflags/nowarpto.txt",
"npc2/mapflags/pvp.txt",
"npc2/mapflags/itemunlock.txt",
"npc2/mapflags/noautoattack.txt",


//============================================================
//===== Mobs [A-Z] ==================================
//============================================================
//"npc2/mobs/guardo.txt",
//"npc2/mobs/mvp_mobs.txt",
"npc2/mobs/thana.txt",

//============================================================
//===== Event Scripts [A-Z] ==================================
//============================================================
"npc2/event/monthsary/compe.txt",
"npc2/event/monthsary/freejob.txt",
"npc2/event/monthsary/x2event.txt",
"npc2/event/void.txt",
"npc2/event/voidmini.txt",
"npc2/event/guildflag.txt",
"npc2/event/hourlyreward.txt",
"npc2/event/hourlypoints.txt",
"npc2/event/invasion.txt",
"npc2/event/arenainvasion.txt",
"npc2/event/koe.txt",
"npc2/event/koe_timer.txt",
"npc2/event/koe_mvp.txt",
"npc2/event/cockpit.txt",
"npc2/event/itemreward.txt",
//"npc2/event/kids_timer.txt",
//"npc2/event/kids_mvp.txt",
"npc2/event/koth.txt",
"npc2/event/poringcatcher.txt",
"npc2/event/race.txt",
"npc2/event/gacha.txt",
"npc2/event/gacha_artifact.txt",
"npc2/event/worldboss.txt",
"npc2/event/dice.txt",
"npc2/event/eventhelper.txt",
"npc2/event/eventmanager.txt",
"npc2/event/safe.txt",
//"npc2/event/auction.txt",
"npc2/event/auctioncreds.txt",
"npc2/event/auctiondt.txt",
"npc2/event/poringsummoner.txt",
"npc2/event/luckypick.txt",
//"npc2/event/prace.txt",
"npc2/event/nvz.txt",
"npc2/event/mvphunting.txt",
//"npc2/event/pubg.txt",

//"npc2/event/summer/atsummer.txt",
//"npc2/event/summer/raffle.txt",
//"npc2/event/summer/rotd.txt",
//"npc2/event/summer/summershop.txt",

//"npc2/event/valentine/rotd.txt",
//"npc2/event/valentine/cmd.txt",

//"npc2/event/halloween/papajakk.txt",
//"npc2/event/halloween/refineshop.txt",
//"npc2/event/halloween/cmd.txt",
//"npc2/event/halloween/questshop.txt",
//"npc2/event/halloween/hollowporing.txt",

//"npc2/event/xmas/cmd.txt",
//"npc2/event/xmas/camp.txt",
//"npc2/event/xmas/xmascards.txt",
//"npc2/event/xmas/xmasboss.txt",
//"npc2/event/xmas/questshop.txt",
//"npc2/event/xmas/xmassock.txt",
//"npc2/event/xmas/refineshop.txt",
//"npc2/event/xmas/exchange.txt",

//============================================================
//===== Temp Folder [A-Z] ====================================
//============================================================
"npc2/temp/peak.txt",
"npc2/temp/agitstatus.txt",

//============================================================
//===== Battlegrounds [A-Z] ==================================
//============================================================
"npc2/eamodbg/bg_common.txt",
"npc2/eamodbg/bg_conquest.txt",
"npc2/eamodbg/bg_flavius_td.txt",
"npc2/eamodbg/bgshop.txt",
//"npc2/eamodbg/bgantidual.txt",
//"npc2/eamodbg/bghour.txt",

//============================================================
"npc2/osro_ach.txt",

//============================================================
"npc2/new/castlereset.txt",
"npc2/new/costumerefine.txt",
"npc2/new/refinetransfer.txt",