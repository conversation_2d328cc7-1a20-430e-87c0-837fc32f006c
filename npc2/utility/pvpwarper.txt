/*
	ALTER TABLE `char` ADD `pvp_notification` TINYINT(3) UNSIGNED NOT NULL;
*/

prontera,202,136,3	script	PvP Warper	4_M_KNIGHT_BLACK,{
	if(agitcheck() == 1) {
			mes "[ ^996600PvP Warper^000000 ]";
			mes "-------------------------------";
			mes "You can't enter during WoE.";
			next;
			close;
	}
	if (Class == 0 || Class == 4001 || Class == 23 || Class == 4023) {
			mes "[ ^996600PvP Warper^000000 ]";
			mes "-------------------------------";
			mes "Sorry, <PERSON><PERSON> is not allowed to enter.";
			next;
			close;
	}
	set .@gid,getcharid(0);
	query_sql( "SELECT `pvp_notification` FROM `char` WHERE `char_id` = "+.@gid+" LIMIT 1",.@notification );

	switch( select( 
		"Restricted ^996600@go 15^000000 [ " + getmapusers("new_1-1") + " ]",
		"Champion Room [ " + getmapusers("champ_room") + " ]",
		"Assassin Cross Room [ " + getmapusers("pvp_y_1-5") + " ]",
		"Restricted SinX Room [ " + getmapusers("sinx_room") + " ]",
		"PvP Points Shop",
		( .@notification )?"[^61A302ON^000000] Disable Notification.":"[^FF0000OFF^000000] Notify me when someone enter.")
	) {
		case 1:
			if (strcharinfo(3) == "new_1-1") { dispbottom "You're already here!"; end; }
			if("new_1-1" == "force_3-2")
				warp "new_1-1",rand( .warpto_area[0], .warpto_area[2] ), rand( .warpto_area[1], .warpto_area[3] );
			else
				warp "new_1-1",0,0;
			query_sql( "SELECT `account_id`,`char_id` FROM `char` WHERE `char_id` <> "+.@gid+" AND `online` = 1 AND `pvp_notification` = 1 LIMIT 128",.@aid,.@cid );
			set .@aid_size,getarraysize( .@aid );
			set .@char_name$, strcharinfo(0);
			while ( .@i < .@aid_size ) {
				if ( isloggedin( .@aid[.@i],.@cid[.@i] ) )
					if ( attachrid( .@aid[.@i] ) )
						if ( strcharinfo( 3 ) == "new_1-1" ) 
							//dispbottom .@char_name$+" entered PVP Room!";
							announce .@char_name$+" entered PvP Room!",bc_self;
				set .@i,.@i + 1;

			}
			detachrid;
			end;
		case 2:
			if (getmapflag(strcharinfo(3),mf_pvp) || getmapflag(strcharinfo(3),mf_gvg) || getmapflag(strcharinfo(3),mf_battleground)) {
				message strcharinfo(0),"You cannot use this command in Battlefield area.";
				close;
			}
			if(agitcheck() == 1) {
				message strcharinfo(0),"You cannot enter during WoE.";
				close;
			}
			if (Class != Job_Champion) {
				message strcharinfo(0),"Only Champions may enter this room.";
				close;
			}

			if (strcharinfo(3) == "champ_room") { dispbottom "You're already here!"; end; }

			warp "champ_room",98+(rand(-10,10)),102+(rand(-10,10));
			query_sql( "SELECT `account_id`,`char_id` FROM `char` WHERE `char_id` <> "+.@gid+" AND `online` = 1 AND `pvp_notification` = 1 LIMIT 128",.@aid,.@cid );
			set .@aid_size,getarraysize( .@aid );
			set .@char_name$, strcharinfo(0);
			while ( .@i < .@aid_size ) {
				if ( isloggedin( .@aid[.@i],.@cid[.@i] ) )
					if ( attachrid( .@aid[.@i] ) )
						if ( strcharinfo( 3 ) == "champ_room" ) 
							//dispbottom .@char_name$+" entered PVP Room!";
							announce .@char_name$+" entered PvP Room!",bc_self;
				set .@i,.@i + 1;

			}
			detachrid;
			end;
		case 3:
			if (getmapflag(strcharinfo(3),mf_pvp) || getmapflag(strcharinfo(3),mf_gvg) || getmapflag(strcharinfo(3),mf_battleground)) {
				message strcharinfo(0),"You cannot use this command in Battlefield area.";
				close;
			}
			if(agitcheck() == 1) {
				message strcharinfo(0),"You cannot enter during WoE.";
				close;
			}
			if (BaseJob != Job_Assassin) {
				message strcharinfo(0),"Only Assassin Cross may enter this room.";
				close;
			}
			if (strcharinfo(3) == "pvp_y_1-5") { dispbottom "You're already here!"; end; }

			warp "pvp_y_1-5",159+(rand(-10,10)),95+(rand(-10,10));
			query_sql( "SELECT `account_id`,`char_id` FROM `char` WHERE `char_id` <> "+.@gid+" AND `online` = 1 AND `pvp_notification` = 1 LIMIT 128",.@aid,.@cid );
			set .@aid_size,getarraysize( .@aid );
			set .@char_name$, strcharinfo(0);
			while ( .@i < .@aid_size ) {
				if ( isloggedin( .@aid[.@i],.@cid[.@i] ) )
					if ( attachrid( .@aid[.@i] ) )
						if ( strcharinfo( 3 ) == "champ_room" ) 
							//dispbottom .@char_name$+" entered PVP Room!";
							announce .@char_name$+" entered PvP Room!",bc_self;
				set .@i,.@i + 1;

			}
			detachrid;
			end;
		case 4:
			if (getmapflag(strcharinfo(3),mf_pvp) || getmapflag(strcharinfo(3),mf_gvg) || getmapflag(strcharinfo(3),mf_battleground)) {
				message strcharinfo(0),"You cannot use this command in Battlefield area.";
				close;
			}
			if(agitcheck() == 1) {
				message strcharinfo(0),"You cannot enter during WoE.";
				close;
			}
			if (BaseJob != Job_Assassin) {
				message strcharinfo(0),"Only Assassin Cross may enter this room.";
				close;
			}
			if (strcharinfo(3) == "sinx_room") { dispbottom "You're already here!"; end; }
			
			sc_end(SC_ALL);
			warp "sinx_room",98+(rand(-10,10)),102+(rand(-10,10));
			query_sql( "SELECT `account_id`,`char_id` FROM `char` WHERE `char_id` <> "+.@gid+" AND `online` = 1 AND `pvp_notification` = 1 LIMIT 128",.@aid,.@cid );
			set .@aid_size,getarraysize( .@aid );
			set .@char_name$, strcharinfo(0);
			while ( .@i < .@aid_size ) {
				if ( isloggedin( .@aid[.@i],.@cid[.@i] ) )
					if ( attachrid( .@aid[.@i] ) )
						if ( strcharinfo( 3 ) == "sinx_room" ) 
							//dispbottom .@char_name$+" entered PVP Room!";
							announce .@char_name$+" entered PvP Room!",bc_self;
				set .@i,.@i + 1;

			}
			detachrid;
			end;
		case 5:
			mes "[ ^996600PvP Warper^000000 ]";
			mes "Welcome to PvP Points Shop";
			mes "Seems like you own em all inside the PvP Room?";
			mes "So? Maybe I will just say happy shopping!?";
			openshop("PvP_PtsShop");
			end;
	
		case 8:
			set .@notification,!.@notification;
			query_sql( "UPDATE `char` SET `pvp_notification` = "+.@notification+" WHERE `char_id` = "+.@gid+" LIMIT 1" );
			mes "[ ^996600PvP Warper^000000 ]";
			mes "Notification "+( ( .@notification ) ? "Enabled":"Disabled" )+".";
			close;
		default:
			break;
	}
	close;
	
	OnInit:
		bindatcmd("pvp", strnpcinfo(3)+"::OnAtcommand",0,99);
		setarray $@pvp_map2$, "force_3-2","guild_vs3";
		setarray .warpto_area,158,114,189,189;
		setarray .mapflags,mf_pvp,
						mf_nomemo,
						mf_noteleport,
						mf_nowarp,
						mf_nowarpto,
						mf_pvp_noparty,
						mf_allowpk,
						mf_pvp_noguild;
		.@sz = getarraysize($@pvp_map2$);
		.@msz = getarraysize(.mapflags);
		for(.@i = 0; .@i < .@sz; .@i++)
			for(.@j = 0; .@j < .@msz; .@j++)
				setmapflag $@pvp_map2$[.@i],.mapflags[.@j];
		callsub(S_SetWatingRoom);
		initnpctimer;

	OnWhisperGlobal:
		if ( playerattached() ) {
			if (getgroupid() < 90) end;
		}
	OnClock0001:
		$@pvp_mapidx = rand(getarraysize($@pvp_map2$));
		end;
		
	OnTimer5000:
		callsub(S_SetWatingRoom);
		end;
	
	S_SetWatingRoom:
		delwaitingroom;
		waitingroom "PvP Warper [ "+(getmapusers("sinx_room")+getmapusers($@pvp_map2$[$@pvp_mapidx])+getmapusers("new_1-1")+getmapusers("champ_room")+getmapusers("pvp_y_1-5")+getmapusers("guild_vs4")+getareausers("force_3-2", 209, 175, 230, 160))+" ]",0;
		initnpctimer;
		return;
}

-	script	PvPFunc	-1,{

OnPCDieEvent:
	if (agitcheck() || agitcheck2() || $@koe_status) { dispbottom "No PvP Points can be obtained during WoE/KoE"; end; }
	
	getmapxy(@pvp$,@px,@py,0);
	if(@pvp$ == "new_1-1") {
		set pvppoints,pvppoints-1;
		if(pvppoints < 0) {
			set pvppoints,0; 
		}
		dispbottom "You've been killed by "+rid2name(killerrid)+", you lose 1 PvP Point.";
	}
	end;

OnInit:
	setarray $@pvp_map$, "force_3-2","guild_vs3","new_1-1";
	setarray .warpto_area,158,114,189,189;
	end;
}


-	trader	PvP_PtsShop	-1,{
	OnInit:
	tradertype(NST_CUSTOM);
	sellitem 44156,500;
	sellitem 12075,5;
	sellitem 12080,5;
	sellitem 12085,5;
	sellitem 12090,5;
	sellitem 12095,5;
	sellitem 12100,5;
	sellitem 12123,7;
	sellitem 14541,7;
	sellitem 14543,7;
	sellitem 14542,15;
	sellitem 14544,15;
	sellitem 14517,1;
	sellitem 14518,1;
	sellitem 14519,1;
	sellitem 14520,1;
	sellitem 12218,7;
	sellitem 7621,5;
	end;
	
OnCountFunds:
	setcurrency(pvppoints); end;

OnPayFunds:
	if( pvppoints < @price ) end;
	pvppoints = pvppoints - @price;
	purchaseok(); end;
}

force_3-2,158,177,3	script	Exit#PvP1	2_BOARD1,{	
	dispbottom "Leaving PvP Room in 10 seconds...";
	progressbar "red",10;
	warp "prontera",173,143;	
	end;
}

guild_vs3,50,87,3	duplicate(Exit#PvP1)	Exit#PvP2	2_BOARD1
pvp_y_1-2,129,153,3	duplicate(Exit#PvP1)	Exit#PvP3	2_BOARD1


//============== PVP POINTS ============
-	script	PvPpoints	-1,{

OnPCKillEvent:
if (agitcheck() || agitcheck2() || $@koe_status) { dispbottom "No PvP Points can be obtained during WoE/KoE"; end; }
getmapxy(@pvp$,@px,@py,0);
if(@pvp$ == "new_1-1") {

if (@pvp$ != "new_1-1")
	set pvppoints,pvppoints+1;
else {
	if (@px >= 40 && @px <= 77 && @py >= 76 && @py <= 147)
		set pvppoints,pvppoints+1;
}

if(pvpkilled$ != rid2name(killedrid)) set killrepeat,0;


set pvpkilled$,rid2name(killedrid);


set killrepeat,killrepeat+1;

if(killrepeat >= 99 && pvpkilled$ == rid2name(killedrid)) { 
	set pvppoints,pvppoints-2;
	dispbottom "You've killed the same character more than twice, you lose 1 PvP Point.";
}
else {
	if (@pvp$ != "new_1-1")
		dispbottom "You've killed "+pvpkilled$+", you gain 1 PvP Point.";
	else {
		if (@px >= 40 && @px <= 77 && @py >= 76 && @py <= 147)
			dispbottom "You've killed "+pvpkilled$+", you gain 1 PvP Point.";
	}
} 
}
end;
}


//============== PVP POINTS ============
-	script	PvPpoints2	-1,{

OnPCKillEvent:
getmapxy(@pvp2$,@px,@py,0);
if(@pvp2$ == "schg_cas06" || @pvp2$ == "schg_cas07" || @pvp2$ == "schg_cas08" || @pvp2$ == "arug_cas06" || @pvp2$ == "arug_cas07" || @pvp2$ == "arug_cas08" || @pvp2$ == "bat_b03") {

set pvppoints,pvppoints+1;

if(pvpkilled$ != rid2name(killedrid)) set killrepeat,0;


set pvpkilled$,rid2name(killedrid);


set killrepeat,killrepeat+1;

if(killrepeat >= 3 && pvpkilled$ == rid2name(killedrid)) { 
set pvppoints,pvppoints-2;
dispbottom "You've killed the same character more than twice, you lose 1 PvP Point.";
}
else dispbottom "You've killed "+pvpkilled$+", you gain 1 PvP Point.";
}
end;
}


-	script	PvPdeath2	-1,{

OnPCDieEvent:
getmapxy(@pvp2$,@px,@py,0);
if(@pvp2$ == "schg_cas06" || @pvp2$ == "schg_cas07" || @pvp2$ == "schg_cas08" || @pvp2$ == "arug_cas06" || @pvp2$ == "arug_cas07" || @pvp2$ == "arug_cas08" || @pvp2$ == "bat_b03") {
set pvppoints,pvppoints-1;
if(pvppoints < 0) { set pvppoints,0; }
dispbottom "You've been killed by "+rid2name(killerrid)+", you lose 1 PvP Point.";
}
end;
}

force_3-2	mapflag	allowpk
force_3-2	mapflag	nomemo
force_3-2	mapflag	noteleport
force_3-2	mapflag	nowarp
force_3-2	mapflag	nowarpto
force_3-2	mapflag	pvp_noparty
force_3-2	mapflag	pvp_noguild
pvp_y_1-5	mapflag	allowpk
pvp_y_1-5	mapflag	nomemo
pvp_y_1-5	mapflag	noteleport
pvp_y_1-5	mapflag	nowarp
pvp_y_1-5	mapflag	nowarpto
pvp_y_1-5	mapflag	pvp_noparty
pvp_y_1-5	mapflag	pvp_noguild
champ_room	mapflag	allowpk
champ_room	mapflag	nomemo
champ_room	mapflag	noteleport
champ_room	mapflag	nowarp
champ_room	mapflag	nowarpto
champ_room	mapflag	pvp_noparty
champ_room	mapflag	pvp_noguild

guild_vs4	mapflag	allowpk
guild_vs4	mapflag	nomemo
guild_vs4	mapflag	noteleport
guild_vs4	mapflag	nowarp
guild_vs4	mapflag	nowarpto
guild_vs4	mapflag	gvg	off
guild_vs4	mapflag	pvp_noparty
guild_vs4	mapflag	pvp_noguild
guild_vs4	mapflag	pvp
guild_vs4	mapflag	noknockback


sinx_room	mapflag	allowpk
sinx_room	mapflag	nomemo
sinx_room	mapflag	noteleport
sinx_room	mapflag	nowarp
sinx_room	mapflag	nowarpto
sinx_room	mapflag	gvg	off
sinx_room	mapflag	pvp_noparty
sinx_room	mapflag	pvp_noguild
sinx_room	mapflag	pvp


morocc,162,95,3	duplicate(PvP Warper)	PvP Warper#1	4_M_JOB_KNIGHT1
payon,148,230,5	duplicate(PvP Warper)	PvP Warper#2	4_M_JOB_KNIGHT1
geffen,115,67,5	duplicate(PvP Warper)	PvP Warper#10	4_M_JOB_KNIGHT1
alberta,176,144,5	duplicate(PvP Warper)	PvP Warper#11	4_M_JOB_KNIGHT1
izlude,131,121,3	duplicate(PvP Warper)	PvP Warper#12	4_M_JOB_KNIGHT1
aldebaran,133,112,5	duplicate(PvP Warper)	PvP Warper#13	4_M_JOB_KNIGHT1
xmas,154,139,5	duplicate(PvP Warper)	PvP Warper#14	4_M_JOB_KNIGHT1
comodo,185,157,3	duplicate(PvP Warper)	PvP Warper#15	4_M_JOB_KNIGHT1
yuno,166,184,3	duplicate(PvP Warper)	PvP Warper#16	4_M_JOB_KNIGHT1
amatsu,99,152,5	duplicate(PvP Warper)	PvP Warper#17	4_M_JOB_KNIGHT1
gonryun,166,126,3	duplicate(PvP Warper)	PvP Warper#18	4_M_JOB_KNIGHT1
louyang,224,107,3	duplicate(PvP Warper)	PvP Warper#19	4_M_JOB_KNIGHT1
ayothaya,225,179,3	duplicate(PvP Warper)	PvP Warper#20	4_M_JOB_KNIGHT1
einbroch,248,205,3	duplicate(PvP Warper)	PvP Warper#21	4_M_JOB_KNIGHT1
lighthalzen,150,100,3	duplicate(PvP Warper)	PvP Warper#22	4_M_JOB_KNIGHT1
hugel,103,141,3	duplicate(PvP Warper)	PvP Warper#23	4_M_JOB_KNIGHT1
rachel,122,135,3	duplicate(PvP Warper)	PvP Warper#24	4_M_JOB_KNIGHT1
splendide,202,184,5	duplicate(PvP Warper)	PvP Warper#25	4_M_JOB_KNIGHT1
brasilis,189,217,5	duplicate(PvP Warper)	PvP Warper#26	4_M_JOB_KNIGHT1

/*
guild_vs4,55,55,3	script	Enchant Deadly Saucer#buff	4_GEFFEN_08,{
	.@edpreq = 0;
	if (.@edpreq) {
		if (countitem(678)) {
			delitem 678,1;
			sc_start(SC_EDP, 50000, 3, 10000);
		} else {
			dispbottom "You need a Poison Bottle to get EDP.";
			end;
		}
	} else {
		if (@edpbuffdelay < gettimetick(2)) {
			sc_start(SC_EDP, 120000, 3, 10000);
			specialeffect2 493;
			sc_start(SC_INC_AGI, 120000, 10, 10000);
			sc_start(SC_BLESSING, 120000, 10, 10000);
			@edpbuffdelay = gettimetick(2)+30;
		} else {
			dispbottom "Please wait "+(@edpbuffdelay-gettimetick(2))+" seconds before getting a buff again.";
			end;
		}
	}
	
	end;
}
*/

guild_vs4,38,61,5	script	Exit#gvg4	4_TOWER_05,{
	dispbottom "Leaving PvP Room in 10 seconds...";
	progressbar "red",10;
	sc_end(SC_ALL);
	warp "prontera",173,143;
	end;
}

sinx_room,110,110,3	script	Arena Buffer	4_F_GUILLOTINE,{

if( healer > gettimetick(2) ) end;
else {
percentheal 100,100;
set healer,gettimetick(2)+60;
skilleffect 28,6969;
skilleffect 34,0;
skilleffect 29,0;
skilleffect 479,0;
sc_start SC_BLESSING,900000,10;
sc_start SC_INC_AGI,900000,10;
sc_start SC_PROTECTWEAPON,900000,5;
sc_start SC_PROTECTARMOR,900000,5;
sc_start SC_FOOD_STR, 600000, 10; 
sc_start SC_FOOD_AGI, 600000, 10; 
sc_start SC_FOOD_VIT, 600000, 10; 
sc_start SC_FOOD_DEX, 600000, 10; 
sc_start SC_FOOD_LUK, 600000, 10; 

		if( getbrokenid(1) ) repairall;
	if( ( 0 <= Class && Class <= 6 ) || ( 4001 <= Class && Class <= 4007 ) || Class == 24 || Class == 25 || Class == 4230 || Class == 4233 || Class == 4234 ) { }
	else {
		switch ( BaseJob ) {     
			case 18:    
				.@spirit = 445; 
				break;        
			case 20:      
			case 15:    
				.@spirit = 447;
				break;       
			case 19:    
				.@spirit = 455; 
				break;       
			case 4047:  
				.@spirit = 448;
				break;       
			case 17:    
				.@spirit = 456;
				break;       
			case 16:   
				.@spirit = 449;
				break;     
			case 12:   
				.@spirit = 457; 
				break;       
			case 14:    
				.@spirit = 450;
				break;       
			case 10:  
				.@spirit = 458; 
				break;       
			case 23:    
				.@spirit = 451; 
				break;     
			case 11:  
				.@spirit = 460;
				break;      
			case 7:     
				.@spirit = 452;
				break;        
			case 4049:  
				.@spirit = 461; 
				break;       
			case 8:   
				.@spirit = 454;
				break;      
			case 9:     
				.@spirit = 453; 
				break;    
		}  
		sc_start4 SC_SOULLINK, 240000, 1, .@spirit, 0, 0;  
		skilleffect2 .@spirit, 1; // Start Soul Link Effect.  
	}
	
}

}