//===== Hercules Script ======================================
//= Euphy's Quest Shop
//===== By: ==================================================
//= Euphy
//===== Current Version: =====================================
//= 1.6a
//===== Description: =========================================
//= A dynamic quest shop based on Lunar's, with easier config.
//= Includes support for multiple shops & cashpoints.
//= Item Preview script by ToastOfDoom.
//===== Additional Comments: =================================
//= 1.0 Initial script.
//= 1.2 Added category support.
//= 1.3 More options and fixes.
//= 1.4 Added debug settings.
//= 1.5 Replaced categories with shop IDs.
//= 1.6 Added support for purchasing stackables.
//= 1.6a Added support for previewing costumes and robes.
//============================================================

// Shop NPCs -- supplying no argument displays entire menu.
//	callfunc "dmshop"{,<shop ID>{,<shop ID>{,...}}};
//============================================================
prontera,160,119,5	script	Donate Manager	4_M_SALVATION,{
	callfunc "dmshop";

OnInit:
	$@donate = 0; // Default promo state: Disabled
	delwaitingroom;
	waitingroom ($@donate ? "Donation Active!" : "Donation Manager!"), 0;
	bindatcmd("promo", strnpcinfo(3) + "::OnDonateCommand", 99, 99);
	end;

OnDonateCommand:
	// Toggle the donation promo system
	if ($@donate) {
		$@donate = 0;
		//announce "The donation system has been disabled.", bc_all;
	} else {
		$@donate = 1;
		//announce "The donation system is now active! Check out the promo details!", bc_all;
	}
	delwaitingroom;
	waitingroom ($@donate ? "Donation Promo Active!" : "Donation Manager!"), 0;
	end;

OnPCLoadMapEvent:
	delwaitingroom;
	waitingroom ($@donate ? "Donation Promo Active!" : "Donation Manager!"), 0;
	end;
}


// Script Core
//============================================================
-	script	quest_dmshop	FAKE_NPC,{
OnWhisperGlobal:
		if ( getgmlevel() < 90 ) end;
function Add; function Chk; function Slot; function A_An;
OnInit:
	freeloop(1);

// -----------------------------------------------------------
//  Basic shop settings.
// -----------------------------------------------------------

	set .Announce,1;	// Announce quest completion? (1: yes / 0: no)
	set .ShowSlot,1;	// Show item slots? (2: all equipment / 1: if slots > 0 / 0: never)
	set .ShowID,0;  	// Show item IDs? (1: yes / 0: no)
	set .ShowZeny,0;	// Show Zeny cost, if any? (1: yes / 0: no)
	set .MaxStack,100;	// Max number of quest items purchased at one time.

// -----------------------------------------------------------
//  Points variable -- optional quest requirement.
//	setarray .@points$[0],"<variable name>","<display name>";
// -----------------------------------------------------------

	

// -----------------------------------------------------------
//  Shop IDs -- to add shops, copy dummy data at bottom of file.
//	setarray .Shops$[1],"<Shop 1>","<Shop 2>"{,...};
// -----------------------------------------------------------

	setarray .Shops$[1],"[-] Rare Shop","[-] Misc Shop","[-] Equipment Shop","[-] Headgear Point Shop","[-] Dragon Weapon Shop","[-] Rare Costume Shop","[-] Weapon Costume Shop","[-] Class Manual Shop";

// -----------------------------------------------------------
//  Quest items -- do NOT use a reward item more than once!
//	Add(<shop ID>,<reward ID>,<reward amount>,
//	    <Zeny cost>,<point cost>,
//	    <required item ID>,<required item amount>{,...});
// -----------------------------------------------------------
	
	Add(1,8423,1,0,75);
	Add(1,8424,1,0,75);
	Add(1,8425,1,0,75);
	Add(1,8426,1,0,75);
	Add(1,30069,1,0,75);
	Add(1,30070,1,0,75);
	Add(1,30071,1,0,75);
	Add(1,30072,1,0,75);
	Add(1,30213,1,0,75);
	Add(1,9426,1,0,150);
	Add(1,9427,1,0,150);
	Add(1,9428,1,0,150);
	Add(1,9429,1,0,150);

	Add(2,3157,1,0,25);
	Add(2,8206,1,0,50);
	Add(2,3877,1,0,35);
	Add(2,3160,1,0,75);
	Add(2,3161,1,0,75);
	Add(2,3162,1,0,150);
	Add(2,12622,1,0,150);
	Add(2,3192,1,0,150);
	Add(2,3143,1,0,160);

	Add(3,5360,1,0,60);
	Add(3,8962,1,0,50);
	Add(3,2541,1,0,70);
	Add(3,3019,1,0,25);
	Add(3,3004,1,0,45);
	Add(3,3668,1,0,25);
	Add(3,3667,1,0,45);
	Add(3,3666,1,0,25);
	Add(3,3665,1,0,45);
	Add(3,3881,1,0,25);
	Add(3,3880,1,0,45);
	Add(3,8088,1,0,25);
	Add(3,8087,1,0,45);
	Add(3,8090,1,0,25);
	Add(3,8089,1,0,45);
	Add(3,8213,1,0,25);
	Add(3,8212,1,0,45);
	Add(3,2554,1,0,22);
	Add(3,2423,1,0,22);
	Add(3,5135,1,0,35);
	Add(3,3746,1,0,35);
	Add(3,3747,1,0,35);
	Add(3,5788,1,0,35);
	Add(3,8607,1,0,35);
	Add(3,8868,1,0,45);
	Add(3,2720,1,0,75);
	Add(3,2721,1,0,75);
	Add(3,2722,1,0,75);
	Add(3,2723,1,0,75);
	Add(3,2724,1,0,75);
	Add(3,2725,1,0,75);

	Add(4,8375,1,0,200);
	Add(4,8380,1,0,200);
	Add(4,8392,1,0,200);
	Add(4,8393,1,0,200);
	Add(4,8394,1,0,200);
	Add(4,8403,1,0,200);
	Add(4,8404,1,0,200);
	Add(4,8405,1,0,200);
	Add(4,8406,1,0,200);
	Add(4,8407,1,0,200);
	Add(4,8410,1,0,200);
	Add(4,8614,1,0,200);
	Add(4,8631,1,0,200);
	Add(4,8287,1,0,200);
	Add(4,8288,1,0,200);
	Add(4,8289,1,0,200);
	Add(4,8290,1,0,200);
	Add(4,8291,1,0,200);
	Add(4,8292,1,0,200);
	Add(4,8293,1,0,200);
	Add(4,8317,1,0,200);
	Add(4,8318,1,0,200);
	Add(4,8319,1,0,200);
	Add(4,8320,1,0,200);
	Add(4,8321,1,0,200);
	Add(4,8322,1,0,200);
	Add(4,8323,1,0,200);

	Add(5,30481,1,0,55);
	Add(5,3515,1,0,33);
	Add(5,3516,1,0,33);
	Add(5,3517,1,0,33);
	Add(5,3518,1,0,33);
	Add(5,3519,1,0,33);
	Add(5,3520,1,0,33);
	Add(5,3521,1,0,33);
	Add(5,3522,1,0,33);
	Add(5,3523,1,0,33);
	Add(5,3524,1,0,33);
	Add(5,3525,1,0,33);
	Add(5,3526,1,0,33);
	Add(5,3639,1,0,33);
	Add(5,3640,1,0,33);

	Add(6,8156,1,0,50);
	Add(6,8157,1,0,50);
	Add(6,8158,1,0,50);
	Add(6,8159,1,0,50);
	Add(6,8160,1,0,50);
	Add(6,8161,1,0,50);
	Add(6,8234,1,0,50);
	Add(6,8263,1,0,50);
	Add(6,8275,1,0,50);
	Add(6,8277,1,0,50);
	Add(6,3902,1,0,50);
	Add(6,3905,1,0,50);
	Add(6,3907,1,0,50);
	Add(6,3911,1,0,50);
	Add(6,3954,1,0,50);
	Add(6,20324,1,0,50);
	Add(6,20337,1,0,50);
	Add(6,20338,1,0,50);
	Add(6,20339,1,0,50);
	Add(6,20349,1,0,50);

	Add(6,21535,1,0,200);
	Add(6,21536,1,0,200);
	Add(6,21537,1,0,200);
	Add(6,21538,1,0,200);
	Add(6,21539,1,0,200);
	Add(6,21540,1,0,200);
	Add(6,21541,1,0,200);
	Add(6,21542,1,0,200);
	Add(6,21543,1,0,200);
	Add(6,21544,1,0,200);
	Add(6,21545,1,0,200);
	Add(6,21546,1,0,200);
	
	Add(7,30476,1,0,150);
	Add(7,30477,1,0,150);
	Add(7,30478,1,0,150);
	Add(7,30479,1,0,150);
	Add(7,30480,1,0,150);
	Add(7,30389,1,0,140);
	Add(7,30390,1,0,140);
	Add(7,30391,1,0,140);
	Add(7,30393,1,0,140);
	Add(7,30394,1,0,140);
	Add(7,30395,1,0,140);
	Add(7,30396,1,0,140);
	Add(7,30397,1,0,140);
	Add(7,30398,1,0,140);
	Add(7,30399,1,0,140);
	Add(7,30400,1,0,140);
	Add(7,30401,1,0,140);
	Add(7,30402,1,0,140);
	Add(7,30403,1,0,140);
	Add(7,30404,1,0,140);
	Add(7,30405,1,0,140);
	Add(7,30406,1,0,140);
	Add(7,30407,1,0,140);

	Add(8,3480,1,0,70);
	Add(8,3481,1,0,70);
	Add(8,3482,1,0,70);
	Add(8,3483,1,0,70);
	Add(8,3484,1,0,70);
	Add(8,3485,1,0,70);
	Add(8,3486,1,0,70);
	Add(8,3487,1,0,70);
	Add(8,3488,1,0,70);
	Add(8,3489,1,0,70);
	Add(8,3490,1,0,70);
	Add(8,3491,1,0,70);
	Add(8,3492,1,0,70);
	Add(8,30189,1,0,70);
	Add(8,30190,1,0,70);
	Add(8,30191,1,0,70);
	Add(8,30192,1,0,70);
	Add(8,30193,1,0,70);
	Add(8,30194,1,0,70);
	Add(8,30195,1,0,70);
	Add(8,30196,1,0,70);
	Add(8,30197,1,0,70);
	Add(8,30198,1,0,70);
	Add(8,30199,1,0,70);
	Add(8,30200,1,0,70);	

// -----------------------------------------------------------

	freeloop(0);
	set .menu$,"";
	for(set .@i,1; .@i<=getarraysize(.Shops$); set .@i,.@i+1) {
		set .menu$, .menu$+.Shops$[.@i]+":";
		npcshopdelitem "dmshop"+.@i,909;
	}
	end;
OnLeave:
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS|PCBLOCK_EQUIP, false);
end;

OnMenu:
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS|PCBLOCK_EQUIP, true);
	addtimer 500, strnpcinfo(3)+"::OnLeave";

    if ($@donate) {
        mes "[ ^FF0000$100 Promo Package^000000 ]";
        mes "-------------------------------";
        mes "You will get 40% additional ^008800Donate Points^000000 and ^008800Headgear Points^000000 when donating! And on top of that, for every $100 donation that you made, you will receive:";
        mes "<ITEM>1x +10 Armor Refine Deed<INFO>3179</INFO></ITEM>,";
        mes "<ITEM>1x +11 Weapon Refine Deed<INFO>3171</INFO></ITEM>,";
        mes "-------------------------------";
        mes "Visit us at ^008800www.osro.gg/donate^000000";
        next;
        mes "[ ^FF0000$200 Promo Package^000000 ]";
        mes "-------------------------------";
        mes "You will receive";
        mes "^0000FF1x Enchant of your choice to any equipment or costume^000000.";
        mes "<ITEM>1x +11 Armor Refine Deed<INFO>3180</INFO></ITEM>";
        mes "<ITEM>1x +10 Accessory Refine Deed<INFO>30206</INFO></ITEM>";
        mes "for every $200 amount of donation.";
        mes "-------------------------------";
        mes "Visit us at ^008800www.osro.gg/donate^000000";
        next;
        mes "[ ^FF0000$400 Promo Package^000000 ]";
        mes "-------------------------------";
        mes "^0000FF1x Hydra Dragon Weapon (Main Weapon) of your choice.^000000.";
        mes "for every $400 donation that you made. Hurry up adventurer and don't miss your chance!";
        mes "-------------------------------";
        mes "Visit us at ^008800www.osro.gg/donate^000000";
        next;
        mes "[ ^FF0000$500 Promo Package^000000 ]";
        mes "-------------------------------";
        mes "You will also receive";
        mes "^0000FF1x Dragonslayer Set (Upper, Mid, Low and Garment).";
        mes "^0000FF1x Max Option Enchant (Any Equips) of your choice.^000000. Just visit our wiki page for available option enchants.";
        mes "For every $500 donation that you made..";
        mes "-------------------------------";
        mes "Visit us at ^008800www.osro.gg/donate^000000";
        next;
    }

	mes "[ ^996600Donate Manager^000000 ]";
	mes "Good Day Manjer!";
	mes "-------------------------------";
	mes "FYI, for every $1 USD you donate, you will receive 1 ^008800Donate Point^000000 and 1 ^008800Headgear Point^000000.";
	mes "-------------------------------";
	mes "Visit us at ^008800www.osro.gg/donate^000000";
	next;
	mes "[ ^996600Donate Manager^000000 ]";
	mes "Just a few information before you visit our shop.";
	mes "-------------------------------";
	mes "We have 2 different kind of Donate rewards and 2 different kind of shop, you will received both ^008800Donate Points^000000 and ^008800Headgear Points^000000 everytime you donate!";
	mes "-------------------------------";
	mes "Please choose wisely!";
	set .@size, getarraysize(@i);
	if (!.@size) set .@i, select(.menu$);
	else if (.@size == 1) set .@i, @i[0];
	else {
		for(set .@j,0; .@j<.@size; set .@j,.@j+1)
			set .@menu$, .@menu$+.Shops$[@i[.@j]]+":";
		set .@i, @i[select(.@menu$)-1];
		
	}
	deletearray @i[0],getarraysize(@i);
	if (.Shops$[.@i] == "") {
		message strcharinfo(PC_NAME),"An error has occurred.";
		end;
	}
	dispbottom "Select one item at a time.";
	@shopee = .@i;
	callshop "dmshop"+.@i,1;
	npcshopattach "dmshop"+.@i;
	end;

OnBuyItem:
	// .@q[] : RewardID, BoughtAmt, RewardAmt, BaseAmt, ReqZeny, ReqPts, { ReqItem, ReqAmt, ... }
	setarray .@q[0],@bought_nameid[0],((@bought_quantity[0] > .MaxStack)?.MaxStack:@bought_quantity[0]);
	copyarray .@q[3],getd(".q_"+.@q[0]+"[0]"),getarraysize(getd(".q_"+.@q[0]));
	set .@q[2],.@q[1]*.@q[3];
	if (!.@q[2] || .@q[2] > 30000) {
		message strcharinfo(PC_NAME),"You can't purchase that many "+getitemname(.@q[0])+".";
		end;
	}

	if (@shopee==1) {
		setarray .@points$[0],"#DONATEPOINTS","Donate Points";
	} else if (@shopee==2) {
		setarray .@points$[0],"#DONATEPOINTS","Donate Points";
	} else if (@shopee==3) {
		setarray .@points$[0],"#DONATEPOINTS","Donate Points";
	} else if (@shopee==4) {
		setarray .@points$[0],"#COSTUMEPOINTS","Headgear Points";
	} else if (@shopee==5) {
		setarray .@points$[0],"#DONATEPOINTS","Donate Points";
	} else if (@shopee==6) {
		setarray .@points$[0],"#DONATEPOINTS","Donate Points";
	} else if (@shopee==7) {
		setarray .@points$[0],"#DONATEPOINTS","Donate Points";
	} else if (@shopee==8) {
		setarray .@points$[0],"#DONATEPOINTS","Donate Points";
	}

	mes "[ ^996600Donate Manager^000000 ]";
	mes "Item: ^0055FF"+((.@q[2] > 1)?.@q[2]+"x ":"")+Slot(.@q[0])+"^000000";
	mes "Points Required:";
	if (.@q[4]) mes " > "+Chk(Zeny,.@q[4]*.@q[1])+(.@q[4]*.@q[1])+" Zeny^000000";
	if (.@q[5]) mes " > "+Chk(getd(.@points$[0]),.@q[5]*.@q[1])+(.@q[5]*.@q[1])+" "+.@points$[1]+" ("+getd(.@points$[0])+"/"+(.@q[5]*.@q[1])+")^000000";
	if (.@q[6]) for(set .@i,6; .@i<getarraysize(.@q); set .@i,.@i+2)
		mes " > "+Chk(countitem(.@q[.@i]),.@q[.@i+1]*.@q[1])+((.ShowID)?"{"+.@q[.@i]+"} ":"")+Slot(.@q[.@i])+" ("+countitem(.@q[.@i])+"/"+(.@q[.@i+1]*.@q[1])+")^000000";
	next;
	setarray @qe[1], getiteminfo(.@q[0], ITEMINFO_LOC), getiteminfo(.@q[0], ITEMINFO_VIEWSPRITE);
	if (@qe[2] > 0 && ((@qe[1] & EQP_HEAD_LOW) || (@qe[1] & EQP_HEAD_TOP) || (@qe[1] & EQP_HEAD_MID) || (@qe[1] & EQP_COSTUME_HEAD_TOP) || (@qe[1] & EQP_COSTUME_HEAD_MID) || (@qe[1] & EQP_COSTUME_HEAD_LOW) || (@qe[1] & EQP_GARMENT) || (@qe[1] & EQP_COSTUME_GARMENT)))
		set .@preview,1;
	addtimer 1000, strnpcinfo(NPC_NAME)+"::OnEnd";
	while(1) {
		switch(select("[-] Purchase ^0055FF"+ getitemname(.@q[0]) +"^000000", ((.@preview && !@qe[7])?"[-] Headgear Preview...": ""), "[-] ^777777Cancel^000000")) {
		case 1:
			if (@qe[0]) {
				mes "[ ^996600Donate Manager^000000 ]";
				mes "Sorry! But you're missing one or more points requirements.";
				close;
			}
			if (!checkweight(.@q[0],.@q[2])) {
				mes "[ ^996600Donate Manager^000000 ]";
				mes "^FF0000You need "+(((.@q[2] * getiteminfo(.@q[0], ITEMINFO_WEIGHT)) + Weight - MaxWeight) / 10)+" additional weight capacity to complete this trade.^000000";
				close;
			}
			if (.@q[4]) Zeny -= (.@q[4]*.@q[1]);
			if (.@q[5]) setd .@points$[0], getd(.@points$[0])-(.@q[5]*.@q[1]);
			if (.@q[6]) for(set .@i,6; .@i<getarraysize(.@q); set .@i,.@i+2)
				delitem .@q[.@i],.@q[.@i+1]*.@q[1];
			getitem .@q[0],.@q[2];
			if (.Announce) announce "["+strcharinfo(0)+"] bought "+((.@q[2] > 1)?.@q[2]+"x "+getitemname(.@q[0]):A_An(getitemname(.@q[0])))+" from Donation Manager!",bc_all,0x00FFFF;
			specialeffect(EF_FLOWERLEAF, AREA, playerattached());
			close;
		case 2:
			setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS|PCBLOCK_EQUIP, true);
			setarray @qe[3], getlook(LOOK_HEAD_BOTTOM), getlook(LOOK_HEAD_TOP), getlook(LOOK_HEAD_MID), getlook(LOOK_ROBE), 1;
			if ((@qe[1] & 1) || (@qe[1] & 4096)) changelook LOOK_HEAD_BOTTOM, @qe[2];
			else if ((@qe[1] & 256) || (@qe[1] & 1024)) changelook LOOK_HEAD_TOP, @qe[2];
			else if ((@qe[1] & 512) || (@qe[1] & 2048)) changelook LOOK_HEAD_MID, @qe[2];
			else if ((@qe[1] & 4) || (@qe[1] & 8192)) changelook LOOK_ROBE, @qe[2];
			break;
		case 3:
			close;
		}
	}

OnEnd:
	if (@qe[7]) {
		changelook LOOK_HEAD_BOTTOM, @qe[3];
		changelook LOOK_HEAD_TOP, @qe[4];
		changelook LOOK_HEAD_MID, @qe[5];
		changelook LOOK_ROBE, @qe[6];

	}
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS|PCBLOCK_EQUIP, false);

	deletearray @qe[0],8;
	end;

function Add {
	if (getitemname(getarg(1)) == "null") {
		debugmes "Quest reward #"+getarg(1)+" invalid (skipped).";
		return;
	}
	setarray .@j[0],getarg(2),getarg(3),getarg(4);
	for(set .@i,5; .@i<getargcount(); set .@i,.@i+2) {
		if (getitemname(getarg(.@i)) == "null") {
			debugmes "Quest requirement #"+getarg(.@i)+" invalid (skipped).";
			return;
		} else
			setarray .@j[.@i-2],getarg(.@i),getarg(.@i+1);
	}
	copyarray getd(".q_"+getarg(1)+"[0]"),.@j[0],getarraysize(.@j);
	npcshopadditem "dmshop"+getarg(0),getarg(1),((.ShowZeny)?getarg(3):0);
	return;
}

function Chk {
	if (getarg(0) < getarg(1)) {
		set @qe[0],1;
		return "^FF0000";
	} else
		return "^61A302";
}

function Slot {
	set .@s$,getitemname(getarg(0));
	switch(.ShowSlot) {
		case 1: if (!getitemslots(getarg(0))) return .@s$;
		case 2: if (getiteminfo(getarg(0), ITEMINFO_TYPE) == IT_WEAPON || getiteminfo(getarg(0), ITEMINFO_TYPE) == IT_ARMOR) return .@s$+" ["+getitemslots(getarg(0))+"]";
		default: return .@s$;
	}
}

function A_An {
	setarray .@A$[0],"a","e","i","o","u";
	set .@B$, "_"+getarg(0);
	for(set .@i,0; .@i<5; set .@i,.@i+1)
		if (compare(.@B$,"_"+.@A$[.@i])) return "an "+getarg(0);
	return "a "+getarg(0);
}
}

function	script	dmshop	{
	deletearray @i[0],getarraysize(@i);
	for(set .@i,0; .@i<getargcount(); set .@i,.@i+1)
		set @i[.@i],getarg(.@i);
	doevent "quest_dmshop::OnMenu";
	end;
}


// Dummy shop data -- copy as needed.
//============================================================
-	shop	dmshop1	FAKE_NPC,909:-1
-	shop	dmshop2	FAKE_NPC,909:-1
-	shop	dmshop3	FAKE_NPC,909:-1
-	shop	dmshop4	FAKE_NPC,909:-1
-	shop	dmshop5	FAKE_NPC,909:-1
-	shop	dmshop6	FAKE_NPC,909:-1
-	shop	dmshop7	FAKE_NPC,909:-1
-	shop	dmshop8	FAKE_NPC,909:-1




// Headgear Rotation
//============================================================
	// September 2024
	//Add(4,21656,1,0,100);
	//Add(4,21657,1,0,100);
	//Add(4,21625,1,0,100);
	//Add(4,21624,1,0,120);
	//Add(4,21633,1,0,120);
	//Add(4,21619,1,0,120);
	//Add(4,21626,1,0,120);
	//Add(4,21879,1,0,120);
	//Add(4,21880,1,0,120);
	//Add(4,21881,1,0,120);
	//Add(4,21882,1,0,120);
	//Add(4,21883,1,0,120);
	//Add(4,21884,1,0,120);
	//Add(4,21885,1,0,120);
	//Add(4,21886,1,0,120);
	//Add(4,21887,1,0,120);
	//Add(4,21888,1,0,120);
	//Add(4,21889,1,0,120);
	//Add(4,21629,1,0,125);
	//Add(4,21627,1,0,125);
	//Add(4,21632,1,0,130);
	//Add(4,21618,1,0,130);
	//Add(4,21646,1,0,150);
	//Add(4,21621,1,0,150);
	//Add(4,21697,1,0,200);
	//Add(4,21630,1,0,300);
	//Add(4,21577,1,0,125);
	//Add(4,21578,1,0,125);
	//Add(4,21579,1,0,125);
	//Add(4,21580,1,0,125);
	//Add(4,21581,1,0,125);
	//Add(4,21582,1,0,125);
	//Add(4,21583,1,0,125);
	//Add(4,21584,1,0,125);
	//Add(4,21585,1,0,125);
	//Add(4,21586,1,0,125);
	//Add(4,21587,1,0,125);
	//Add(4,21588,1,0,125);
	//Add(4,21485,1,0,60);
	//Add(4,21492,1,0,60);
	//Add(4,21530,1,0,80);
	//Add(4,21522,1,0,80);
	//Add(4,21478,1,0,80);
	//Add(4,21529,1,0,80);
	//Add(4,21481,1,0,90);
	//Add(4,21532,1,0,90);
	//Add(4,21531,1,0,100);
	//Add(4,21547,1,0,100);
	//Add(4,21548,1,0,100);
	//Add(4,21474,1,0,100);
	//Add(4,21520,1,0,100);
	//Add(4,21521,1,0,100);
	//Add(4,21453,1,0,100);
	//Add(4,21677,1,0,100);
	//Add(4,21493,1,0,100);
	//Add(4,21471,1,0,100);
	//Add(4,21473,1,0,100);
	//Add(4,21556,1,0,100);
	//Add(4,21484,1,0,125);
	//Add(4,21455,1,0,125);
	//Add(4,21555,1,0,300);