//=============== Project Hercules ========================================
//=   _____             _             _        _   _ 
//=  |  _  \           |_|           / |      | | | |
//=  | |_| |_ ________  _  ____  ____| |__    | |_| | ____  _ ________
//=  |  ___/ '__/  _  \/ \/ __ \/ ___|  __|   |  _  |/ __ \| '___/ ___|
//=  | |   | |  | |_|  | |  ___| |___| |____  | | | |  ___/| |  | |___
//=  \_/   \_/  \_____/| |\_____\____\_____/  \_/ | |\_____\_/   \____\
//=                ____, |                        | |
//=               /______/                        \_|
//=
//=========================================================================
//= Charm System
//================= Description ===========================================
// A special item mechanic that can give players status bonuses,
// element resistance and other special effects.
//================= Current Version =======================================
//= 1.0a
//================= Changelogs ============================================
//= 1.0  - Implementation
//= 1.0a - Added filtering mechanic.
//=========================================================================

prontera,277,177,3	script	Charm System	HIDDEN_NPC,{

	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS, true);
	addtimer 1000, strnpcinfo(3)+"::OnLeave";

	function F_CraftCharm;
	mes .header$;
	mes "What can I do for you, adventurer?";
	mes " ";
	if (CHARM_CNT > 0)
		mesf("^FF0000You currently have %s crafted.", F_CommaPlural(CHARM_CNT, "Charm"));
	next;
	switch(select((!CHARM_Q ? "How to craft ^FF3030Charms^000000?" : ""), 
			(CHARM_Q ? "Can you help me crafting a ^FF3030Charm^000000?" : ""), 
			(CHARM_Q ? "I want to dissolve a ^FF3030Charm^000000." : ""), 
			(CHARM_Q && CHARM_CNT >= 200 ? "Can I re-enchant my ^FF3030Rare Charms^000000?" : ""), 
			"Cancel")) {
		case 1:	// Introduction
			mes .header$;
			mes "Before I craft you a Charm, you must give me one (1) type of ingot from ore smelting.";
			next;
			mes .header$;
			mes "If you can provide one of those, then I will help you how to craft ^FF3030Charms^000000.";
			next;
			if (select("I think I have one here...", "Cancel") == 2) {
				mes .header$;
				mes "If you are ready, just come back and let me help you crafting magical ^FF3030Charms^000000.";
				break;
			}
			if (countitem(Aquastone_Ingot) > 0)
				setarray .@ingot[getarraysize(.@ingot)], Aquastone_Ingot;
			if (countitem(Dragonstone_Ingot) > 0)
				setarray .@ingot[getarraysize(.@ingot)], Dragonstone_Ingot;
			if (countitem(Flamestone_Ingot) > 0)
				setarray .@ingot[getarraysize(.@ingot)], Flamestone_Ingot;
			if (!getarraysize(.@ingot)) {
				mes .header$;
				mes "It seems that you do not have the ingot I need.";
				mes " ";
				mes "I guess you need to mine ores and smelt them.";
				break;
			}
			.@ingot_id = .@ingot[rand(getarraysize(.@ingot))];
			switch (.@ingot_id) {
				case Aquastone_Ingot:	.@str$ = sprintf(.color$[0], F_GetItemName(.@ingot_id));	break;
				case Dragonstone_Ingot:	.@str$ = sprintf(.color$[1], F_GetItemName(.@ingot_id));	break;
				case Flamestone_Ingot:	.@str$ = sprintf(.color$[2], F_GetItemName(.@ingot_id));	break;
			}
			CHARM_Q |= 1;
			delitem(.@ingot_id, 1);
			mes .header$;
			mesf("This %s is mystical indeed.", .@str$);
			mes "Now that you have";
			mes "provided me what I need...";
			mes "Let me assist you";
			mes "on crafting a magical ^FF3030Charm^000000.";
			next;

		case 2:	// Charm Crafting
			if (!CHARM_Q) break;
			do {
				mes .header$;
				mes "Now, what charm do you want to craft?";
				mes " ";
				if ((CHARM_CNT/100) - CHARM_RARE > 0)
					mes "^FF0000You can craft a Rare Charm now.";
				next;
				.@select = select("Offensive Charm", "Defensive Charm", "Health or Energy Charm", (CHARM_CNT >= 100 ? "Can you enchant ^FF3030Rare Charms^000000 as well?" : ""), "Cancel");
				if (.@select == 5) break;
				.@var$ = .charm_var$[.@select];
				if (getd(.@var$) < .charm_limit[.@select]) {
					switch(.@select) {
						case 1:		setarray .@amt[0], 3, 3, 3;	break;
						case 4:		setarray .@amt[0], 5, 5, 5;	break;
						default:	setarray .@amt[0], 2, 2, 2;	break;
					}
					F_CraftCharm(.@select, .@amt);
					F_CloseCutin();
				}
				if (.@select == 4) {
					if (CHARM_CNT/100 > CHARM_RARE) {
						setarray .@amt[0], 5, 5, 5;
						F_CraftCharm(.@select, .@amt);
						F_CloseCutin();
					}
					mes .header$;
					mes "It seems that you haven't crafted enough charms yet.";
					mes " ";
					mes "In every 100 crafted regular charms, you can craft one ^FF3030Rare Charm^000000.";
					F_CloseCutin();
				}
				.@var$ = sprintf(".charm_list_%d$", .@select);
				.@menu$ = "";
				for (.@i=0; .@i<getarraysize(getd(.@var$)); .@i++)
					.@menu$ += sprintf("%s:", getd(sprintf("%s[%d]", .@var$, .@i)));
				.@menu$ += "Cancel";
				.@idx = select(.@menu$) - 1;
				if (.@idx == getarraysize(getd(.@var$))) break;
				switch(.@select) {
					case 1:
						if (.@idx == 0) setarray .@amt[0], 2, 5, 2;
						else if (.@idx == 1) setarray .@amt[0], 4, 3, 2;
						else if (.@idx == 2) setarray .@amt[0], 1, 4, 4;
						else setarray .@amt[0], 3, 2, 4;
						break;
					case 4:		setarray .@amt[0], 5, 5, 5;	break;
					default:	setarray .@amt[0], 2, 2, 2;	break;
				}
				F_CraftCharm(.@select, .@amt, .@idx);
				F_CloseCutin();
			} while( .@select != 5 );
			mes .header$;
			mes "If you are decided to craft a ^FF3030Charm^000000, just approach me.";
			break;

		case 3:	// Charm Dissolution
			mes .header$;
			mes "Dissolving your charms only returns a fraction of the ingots you have used.";
			mes " ";
			mes "Are you sure about this?";
			next;
			if (select("Yes!", "No") == 2) break;
			.@j = getarraysize(.charm_label$);
			.@menu$ = "";
			for (.@i=1; .@i<.@j; ++.@i) {
				.@add_menu = false;
				for (.@l=0; .@l<getarraysize(getd(".charm_"+.@i)); .@l++) {
					if (countitem(getd(sprintf(".charm_%d[%d]", .@i, .@l))) > 0) {
						.@add_menu = true;
						break;
					}
				}
				if (.@add_menu) {
					.@menu_cnt++;
					.@menu$ += sprintf("%s:", .charm_label$[.@i]);
				} else
					.@menu$ += ":";
			}
			.@menu$ += "Cancel";
			if (!.@menu_cnt) {
				mes .header$;
				mes "It seems that";
				mes "you do not have";
				mes "any ^FF3030Charms^000000 to dissolve.";
				F_CloseCutin();
			}
			mes .header$;
			mes "Select which charm you want to dissolve.";
			next;
			.@select = select(.@menu$);
			if (.@select == .@j) break;
			.@menu$ = "";
			if (.@select != 4) {
				.@var$ = sprintf(".charm_list_%d$", .@select);
				for (.@i=0; .@i<getarraysize(getd(.@var$)); .@i++) {
					.@k = .@i * 3;
					if (countitem(getd(sprintf(".charm_%d[%d]", .@select, .@k))) > 0 ||
						countitem(getd(sprintf(".charm_%d[%d]", .@select, .@k+1))) > 0 ||
						countitem(getd(sprintf(".charm_%d[%d]", .@select, .@k+2)))) {
						.@found++;
						.@menu$ += sprintf("%s:", getd(.@var$+"["+.@i+"]"));
					} else
						.@menu$ += ":";
				}
				.@menu$ += "Cancel";
				if (!.@found) {
					mes .header$;
					mesf("It seems that you do not have any ^FF3030%s^000000 with you.", .charm_label$[.@select]);
					mes " ";
					mes "Come back when you have it.";
					break;
				}
				.@idx = select(.@menu$) - 1;
				.@idx *= 3;
			}
			.@menu$ = "";
			copyarray(.@charms[0], getd(".charm_"+.@select+"["+.@idx+"]"), .@select != 4 ? 3 : getarraysize(getd(".charm_"+.@select)));
			for (.@i=0; .@i<getarraysize(.@charms); .@i++) {
				if (countitem(.@charms[.@i]) > 0) {
					++.@cnt;
					.@menu$ += sprintf("%s [%d]", F_GetItemName(.@charms[.@i]), .@charms[.@i]);
				}
				else
					.@menu$ += "";
				.@menu$ += ":";
			}
			.@menu$ += "Cancel";
			if (!.@cnt) {
				mes .header$;
				mes "It seems that you do not";
				mesf("have any ^FF3030%s^000000", .@select == 4 ? .charm_label$[.@select] : getd(sprintf(".charm_list_%d$[%d]", .@select, .@idx/3)));
				mes "in your inventory.";
				break;
			}
			.@i = select(.@menu$) - 1;
			if (.@i == getarraysize(.@charms)) {
				mes .header$;
				mes "If you are decided to dissolve a ^FF3030Charm^000000, just approach me.";
				break;
			}
			mes .header$;
			mes "So you are going";
			mesf("to dissolve %s.", F_MesItemInfo(.@charms[.@i]));
			mes " ";
			mes "Let me warn you,";
			mes "You can only get";
			mesf("^0000FF%s^000000 of your choosing.", F_CommaPlural(.charm_break[.@select], "Ingot"));
			next;
			if (select("Proceed with the dissolution.", "Cancel") == 2) break;
			mes .header$;
			mes "Select what ingot you want to extract.";
			next;
			.@menu$ = "";
			for (.@j=0; .@j<getarraysize(.ingot); .@j++)
				.@menu$ += sprintf(.color$[.@j]+":", F_GetItemName(.ingot[.@j]));
			.@menu$ += "Cancel";
			.@j = select(.@menu$) - 1;
			if (.@j == getarraysize(.ingot)) break;
			delitem(.@charms[.@i], 1);
			getitem(.ingot[.@j], .charm_break[.@select]);
			mes .header$;
			mesf("The %s has\r\nsuccessfully dissolved.", F_MesItemInfo(.@charms[.@i]));
			mes " ";
			mesf("Here is your ^3366FF%d %s^000000.", .charm_break[.@select], F_MesItemInfo(.ingot[.@j]));
			break;

		case 4:	// Charm Re-roll
			if (.reroll_limit > 0 && CHARM_REROLL >= .reroll_limit) {
				mes .header$;
				mes "My apologies...";
				mes "But you have already reached the re-enchantment limit.";
				mes " ";
				mesf("^FF0000The maximum re-enchantment\r\nattempt is %s.^000000", .reroll_limit);
				close();
			}
			mes .header$;
			mes "Re-enchanting a ^FF3030Rare Charm^000000 requires:";
			mes " - ^3355FF2 ^FF0000SAME^777777 Rare Charms^000000";
			for (.@i=0; .@i<getarraysize(.color$); .@i++)
				mesf(" - ^3355FF2 %s^000000", F_MesItemInfo(.ingot[.@i]));
			if (.reroll_fee > 0)
				mesf(" - ^3355FF%s ^777777Zeny^000000", F_InsertComma(.reroll_fee));
			mesf("with a success rate of ^009900%d%%^000000.", .reroll_rate);
			next;
			if (.reroll_rate > 0) {
				mes .header$;
				mes "Just a reminder,";
				mes "If the re-enchantment fails,";
				mes "the requirements only";
				mes "dissolves not the ^FF3030Rare Charms^000000.";
				next;
			}
			if (.reroll_limit > 0) {
				mes .header$;
				mes "Also, you can only";
				mesf("do the re-enchant\r\nattempt ^FF0000%s^000000.", F_CommaPlural(.reroll_limit - CHARM_REROLL, "time"));
				mes " ";
				mes "After that, you cannot re-enchant anymore.";
				next;
			}
			.@menu$ = "";
			for (.@i=0; .@i<getarraysize(.charm_4); .@i++) {
				if (countitem(.charm_4[.@i]) < 2)
					.@menu$ += "";
				else {
					.@found++;
					.@menu$ += sprintf("%s", F_GetItemName(.charm_4[.@i]));
				}
				.@menu$ += ":";
			}
			if (!.@found) {
				mes .header$;
				mes "It seems that you";
				mes "do not have any";
				mes "pair of the same";
				mes "^FF3030Rare Charms^000000 in";
				mes "your inventory.";
				break;
			}
			mes .header$;
			mes "Select what charm to re-enchant.";
			next;
			.@i = select(.@menu$) - 1;
			.@charm_id = .charm_4[.@i];
			mes .header$;
			mes "Do you want to proceed";
			mes "for the re-enchantment";
			mesf("of your ^3355FF2 %s^000000?", F_MesItemInfo(.@charm_id));
			mes " ";
			mes "^FF0000This cannot be undone.^000000";
			next;
			if (select("Maybe next time.", "Proceed with the ^009900Re-enchantment^000000.") == 1) break;
			copyarray(.@charm_reroll[0], .charm_4[0], getarraysize(.charm_4));
			for (.@j=0; .@j<getarraysize(.@charm_reroll); .@j++) {
				if (.@i == .@j) {
					deletearray(.@charm_reroll[.@j], 1);
					break;
				}
			}
			.@charm_id_n = .@charm_reroll[rand(getarraysize(.@charm_reroll))];
			if (countitem(.@charm_id) < 2)
				.@fail = true;
			if (Zeny < .reroll_fee)
				.@fail = true;
			for (.@j=0; .@j<getarraysize(.ingot); .@j++) {
				if (countitem(.ingot) < 2) {
					.@fail = true;
					break;
				}
			}
			if (.@fail) {
				mes .header$;
				mes "It seems that you do not have enough requirements to proceed.";
				mes " ";
				mes "Make sure to complete your requirements first.";
				break;
			}
			Zeny -= .reroll_fee;
			for (.@k=0; .@k<getarraysize(.ingot); .@k++)
				delitem(.ingot[.@k], 2);
			if (.reroll_limit > 0 && CHARM_REROLL < .reroll_limit)
				++CHARM_REROLL;
			mes .header$;
			mes "Re-enchanting your charms...";
			mes " ";
			.@k = 0;
			while (.@k < 5) {
				setarray .@num[0], 2, 3, 4, 5, 7;
				.@eff_id$ = sprintf("EF_BEGINSPELL%d", .@num[rand(getarraysize(.@num))]);
				specialeffect(getd(.@eff_id$), SELF, playerattached());
				sleep2(1000);
				.@k++;
			}
			.@rate = rand(100);
			if (.reroll_rate > 0 && (.@rate < 50 - (.reroll_rate/2) && .@rate > 50 + (.reroll_rate/2))) {	// Re-enchantment fails
				mes "All of the requirements is dissolved while attempting to re-enchant.";
				break;
			}
			delitem(.@charm_id, 2);
			getitem(.@charm_id_n, 1);
			mes "The re-enchantment is a success!";
			mesf("Here is your %s.", F_MesItemInfo(.@charm_id_n));
			break;

		default:
			break;
	}
	F_CloseCutin();
	end;

OnPCLoadMapEvent:
	if(strcharinfo(3) == strnpcinfo(4)) {
		.@questInProgress = 1;
		showevent 0,0;
		showevent(.@questInProgress ? QTYPE_QUEST : QTYPE_NONE);
	}
end;

OnLeave:
		setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS, false);
		end;

OnInit:
	.header$ = sprintf("[ %s ]", strnpcinfo(NPC_NAME_VISIBLE));
	// Announcement for charm creation.
	// 1 - Lv 1 | 2 - Lv 2 | 4 - Lv 3 | 8 - Rare Charms
	.charm_bc = 1|2|4|8;
	setarray .color$[0], "^3355FF%s^000000", "^FF9933%s^000000", "^CC3399%s^000000";

	setarray .ingot[0],
					Aquastone_Ingot,
					Dragonstone_Ingot,
					Flamestone_Ingot;

	setarray .charm_label$[1],
					"Offensive Charm",
					"Defensive Charm",
					"Health or Energy Charm",
					"Rare Charm";

	setarray .charm_var$[1],
					"CHARM_OFF",
					"CHARM_DEF",
					"CHARM_HE",
					"CHARM_RARE";

	setarray .charm_limit[1], 15, 25, 10, 0;
	setarray .charm_break[1], 3, 2, 2, 5;
	
	setarray .charm_list_1$[0],
					"Power Charm",
					"Magic Charm",
					"Range Charm",
					"Critical Charm";

	setarray .charm_list_2$[0],
					"Neutral Charm",
					"Water Charm",
					"Earth Charm",
					"Fire Charm",
					"Wind Charm",
					"Poison Charm",
					"Holy Charm",
					"Dark Charm",
					"Ghost Charm",
					"Undead Charm";

	setarray .charm_list_3$[0],
					"Health Charm",
					"Energy Charm";
	
	setarray .charm_1[0],
					Power_Charm, Power_Charm_, Power_Charm__,
					Magic_Charm, Magic_Charm_, Magic_Charm__,
					Range_Charm, Range_Charm_, Range_Charm__,
					Critical_Charm, Critical_Charm_, Critical_Charm__;

	setarray .charm_2[0],
					Neutral_Charm, Neutral_Charm_, Neutral_Charm__,
					Water_Charm, Water_Charm_, Water_Charm__,
					Earth_Charm, Earth_Charm_, Earth_Charm__,
					Fire_Charm, Fire_Charm_, Fire_Charm__,
					Wind_Charm, Wind_Charm_, Wind_Charm__,
					Poison_Charm, Poison_Charm_, Poison_Charm__,
					Holy_Charm, Holy_Charm_, Holy_Charm__,
					Dark_Charm, Dark_Charm_, Dark_Charm__,
					Ghost_Charm, Ghost_Charm_, Ghost_Charm__,
					Undead_Charm, Undead_Charm_, Undead_Charm__;

	setarray .charm_3[0],
					Health_Charm, Health_Charm_, Health_Charm__,
					Energy_Charm, Energy_Charm_, Energy_Charm__;

	setarray .charm_4[0],
					Movement_Charm,
					Speed_Charm,
					Delay_Charm,
					Weight_Charm,
					Bonus_Charm,
					Dispell_Charm,
					Hard_Delay_Charm,
					Ignore_Defender_Charm,
					Flee_Charm,
					Magic_Crit_Charm,
					Critical_Defense_Charm,
					Negate_Defense_Charm;

	.reroll_rate = 50;
	.reroll_fee  = 100000000;
	.reroll_limit = 3;
	end;

function	F_CraftCharm	{
	.@idx = getarg(2, -1);
	copyarray(.@amt[0], getarg(1), getarraysize(getarg(1)));
	mes .header$;
	mesf "To craft a random";
	mesf("^FF3030%s^000000, I need:", .charm_label$[getarg(0)]);
	for (.@i=0; .@i<getarraysize(.@amt); .@i++)
		mesf(" - %s %s", sprintf(.color$[.@i], .@amt[.@i]), F_MesItemInfo(.ingot[.@i]));
	mes "Do you want to proceed?";
	next;
	if (select("Proceed!", "Cancel") == 2)
		return 0;
	copyarray(.@charm_list[0], getd(sprintf(".charm_%d[%d]", getarg(0), (.@idx == -1 ? 0 : .@idx) * 3)), .@idx > -1 ? 3 : getarraysize(getd(sprintf(".charm_%d", getarg(0)))));
	if (getarg(0) != 4) {
		do {
			.@chance = rand(100);
			.@base_charm_id = .@charm_list[0];
			.@charm_id = .@charm_list[rand(getarraysize(.@charm_list))];
			switch (.@charm_id - .@base_charm_id) {
				case 0: // Tier I Charm:
					.@rate = 100;
					break;
				case 1: // Tier II Charm:
					.@rate = 10;
					break;
				case 2: // Tier III Charm:
					.@rate = 1;
					break;
			}
		} while (.@chance > .@rate);
	} else
		.@charm_id = .@charm_list[rand(getarraysize(.@charm_list))];
	for (.@i=0; .@i<getarraysize(.@amt); .@i++) {
		if (countitem(.ingot[.@i]) < .@amt[.@i]) {
			.@failed = true;
			break;
		}
	}
	if (.@failed) {
		mes .header$;
		mes "It seems that you do not have enough ingots to craft charms.";
		mes " ";
		mes "Go to mining shop and buy ores first.";
		return 0;
	}
	if (!checkweight(.@charm_id, 1)) {
		mes .header$;
		mes "You cannot carry charms because you are overweight or your inventory is full.";
		mes " ";
		mes "Please clear your inventory first.";
		return 0;
	}
	mes .header$;
	mes "Casting enchantments...";
	while (.@j < 5) {
		setarray .@num[0], 2, 3, 4, 5, 7;
		.@eff_id$ = sprintf("EF_BEGINSPELL%d", .@num[rand(getarraysize(.@num))]);
		specialeffect(getd(.@eff_id$), SELF, playerattached());
		sleep2(1000);
		.@j++;
	}
	.@var$ = .charm_var$[getarg(0)];
	++CHARM_CNT;
	if (getarg(0) != 4 && getd(.@var$) < .charm_limit[getarg(0)])
		setd(.@var$, getd(.@var$) + 1);
	else if (getarg(0) == 4)
		setd(.@var$, getd(.@var$) + 1);
	.@msg$ = "";
    if (getarg(0) != 4) {
        if (((.charm_bc&4) != 0 && .@rate == 1) || ((.charm_bc&2) != 0 && .@rate == 10) || ((.charm_bc&1) != 0 && .@rate == 100))
            .@msg$ = sprintf("[ Charm Manager ] %s has successfully crafted %s.", strcharinfo(PC_NAME), F_GetItemName(.@charm_id));
    } else
        .@msg$ = sprintf("[ Charm Manager ] %s has successfully crafted a Rare Charm, %s.", strcharinfo(PC_NAME), F_GetItemName(.@charm_id));
    announce(.@msg$, bc_npc_, 0xFFFF00);
	getitem(.@charm_id, 1);
	for (.@i=0; .@i<getarraysize(.@amt); .@i++)
		delitem(.ingot[.@i], .@amt[.@i]);
	mes " ";
	mes "Here is your";
	mesf("mystical %s.", F_MesItemInfo(.@charm_id));
	return 1;
}
}
