lasagna,213,274,3	script	<PERSON><PERSON>fish	4_CAT_ADV2,{
	
	addtimer 1000, strnpcinfo(0)+"::On_Leave";
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_COMMANDS, true);
	mes .npcName$;
	mes "Hello, how may I help you?";
	mes " ";
	mes "^008800Fishing Info^000000: ^AA0000Lvl - "+FishLvl+"^000000 | ^0000AAExp - "+FishExp+"^000000";
	mes "^008800Fishing Point(s)^000000: ^AA0000"+#FISHPOINTS+"^000000";
	if(gettimetick(2) < fishing_license) {
		mes "^008800Fishing License: ^AA0000"+((fishing_license)-gettimetick(2))/60/60+" hour(s)^000000";
	}
	menu "How do I fish?",-,"Apply for a Fishing License",<PERSON><PERSON><PERSON>,"Buy Items",<PERSON><PERSON><PERSON>,"Sell Fish",<PERSON><PERSON>l;
	next;
	mes .npcName$;
	mes "Apply for a ^008800Fishing License^000000. A temporary license will require ^008800Credits^000000.";
	mes "You'll need one of my ^008800Fishing Rods^000000 to catch the fishes here. Don't forget to bring some more ^008800Yummy Worm^000000 as fish bait!";
	mes " ";
	mes "I will buy the fishes for points so you can buy my exclusive goodies.";
	close;
	
	Llicense:
		next;
		mes .npcName$;
		mes "You need to bring me ^0088001,000 Credits^000000 to get a ^000088Fishing License^000000 for a whole month!";
		menu "I have worms!",-;
		next;
		mes .npcName$;
		if(gettimetick(2) < fishing_license) {
			mes "Uhh, based on my records, your license has not yet expired... Why don't you come back later?";
			close;
		}
		if(countitem(3100) < 1000) {
			goto Lnope;
		}
		mes "Congratulations, you can now start fishing!";
		delitem 3100,1000;
		fishing_license = gettimetick(2) + 2592000;
		close;
		
	Lshop:
		next;
		mes .npcName$;
		mes "What would you like to buy?";
		menu "Fishing Rods",Lrod,"Fishing Bait",Lbait,"Fishing Goodies",Lgoods;
		close;
		
	Lbait:
		next;
		mes .npcName$;
		mes "I sell ^008800Yummy Worms^000000 as bait.";
		mes " ";
		mes "Each worm costs ^AA00005,000,000^000000 zeny.";
		mes "How many would you like to buy?";
		next;
		input .@amount;
		if(.@amount < 1) {
			mes .npcName$;
			mes "Please enter a valid amount.";
			close;
		}
		if(Zeny < (.@amount * 5000000)) {
			mes .npcName$;
			mes "You don't have enough zeny!";
			mes "You need ^AA0000"+(.@amount * 5000000)+"^000000 zeny.";
			close;
		}
		Zeny -= (.@amount * 5000000);
		getitem 40117,.@amount;
		mes .npcName$;
		mes "Here are your ^008800"+.@amount+" Yummy Worm(s)^000000!";
		mes "Happy fishing!";
		close;
		
	Lrod:
		next;
		mes .npcName$;
		mes "Which rod would you like to buy?";
		menu "Old Rod",-,"Good Rod",Lr2,"Super Rod",Lr3;
		next;
		mes .npcName$;
		mes "Bring me the following:";
		mes "^008800Yummy Worm^000000 x100";
		mes "^008800"+getitemname(3100)+"^000000 x100";
		mes "^008800100,000,000^000000 Zeny";
		menu "Gotem!",-;
		next;
		mes .npcName$;
		if(countitem(.worm_id) < 100 || Zeny < 100000000 || countitem(3100) < 100) {
			goto Lnope;
		}
		mes "Thanks, have fun fishing!";
		delitem .worm_id,100;
		delitem 3100,100;
		Zeny -= 100000000;
		getitem .rod_id[0],1;
		close;
		
	Lr2:
		next;
		mes .npcName$;
		if(FishLvl < 1) {
			mes "You don't have enough Fishing experience to buy this rod!";
			mes "Current Level: ^00AA00"+FishLvl+"^000000";
			mes "Required Level: ^AA0000"+getarrayindex(.rod_id[1])+"^000000";
			close;
		}
		mes "Bring me the following:";
		mes "^008800Herring^000000 x100";
		mes "^008800Old Rod^000000 x1";
		mes "^008800100,000,000^000000 Zeny";
		menu "Gotem!",-;
		next;
		mes .npcName$;
		if(countitem(.fish_id[0]) < 100 || Zeny < 100000000 || countitem(.rod_id[0]) < 1) {
			goto Lnope;
		}
		mes "Thanks, have fun fishing!";
		delitem .fish_id[0],100;
		delitem .rod_id[0],1;
		Zeny -= 100000000;
		getitem .rod_id[1],1;
		close;
		
	Lr3:
		next;
		mes .npcName$;
		if(FishLvl < 2) {
			mes "You don't have enough Fishing experience to buy this rod!";
			mes "Current Level: ^00AA00"+FishLvl+"^000000";
			mes "Required Level: ^AA0000"+getarrayindex(.rod_id[2])+"^000000";
			close;
		}
		mes "Bring me the following:";
		mes "^008800Tilapia^000000 x100";
		mes "^008800Good Rod^000000 x1";
		mes "^008800100,000,000^000000 Zeny";
		menu "Gotem!",-;
		next;
		mes .npcName$;
		if(countitem(.fish_id[1]) < 100 || Zeny < 100000000 || countitem(.rod_id[1]) < 1) {
			goto Lnope;
		}
		mes "Thanks, have fun fishing!";
		delitem .fish_id[1],100;
		delitem .rod_id[1],1;
		Zeny -= 100000000;
		getitem .rod_id[2],1;
		close;		
		
	Lsell:
		next;
		mes .npcName$;
		mes "I can buy all the fish that you're carrying, do you want to continue?";
		menu "These are mine!",-,"Yes please!",Lfish;
		next;
		mes .npcName$;
		mes "Okay, I was just asking...";
		close;
		
	Lfish:
		next;
		mes .npcName$;
		mes "I will automagically ^AA0000buy all the fish in your inventory^000000, if you want to keep some of them, end this conversation and ^008800put them on your storage^000000.";
		menu "Yes, I ^FF0000totally understand^000000, let's proceed!",-;
		next;
		mes .npcName$;
		for(.@f = 0; .@f < getarraysize(.fish_id); .@f++) {
			setarray(.@fc[.@f], countitem(.fish_id[.@f]));
			setarray(.@fcp[.@f], .@fc[.@f]*(.@f+1));
			.@tf += .@fcp[.@f];
		}
		if(.@tf < 1) {
			mes "You don't have any fish, you just smell like one...";
			close;
		}
		mes "^000088Herring^000000 x^008800"+.@fc[0]+"^000000 @ ^AA00001^000000/pc = ^AA0000"+.@fcp[0]+"pt(s)^000000";
		mes "^000088Tilapia^000000 x^008800"+.@fc[1]+"^000000 @ ^AA00002^000000/pc = ^AA0000"+.@fcp[1]+"pt(s)^000000";
		mes "^000088Milkfish^000000 x^008800"+.@fc[2]+"^000000 @ ^AA00003^000000/pc = ^AA0000"+.@fcp[2]+"pt(s)^000000";
		mes "^008800Total Amount^000000 = ^AA0000"+.@tf+" Point(s)^000000";
		#FISHPOINTS += .@tf;
		message strcharinfo(0),"You earned "+.@tf+" Fishing Point(s) from selling all your fish. (Total: "+#FISHPOINTS+")";
		for(.@d = 0; .@d < getarraysize(.fish_id); .@d++) {
			delitem .fish_id[.@d],.@fc[.@d];
		}
		close;
		
		
	Lgoods:
		close2();
		openshop();
		end;
		
	Lnope:
		mes "I don't think so...";
		close;
	
	On_Leave:
		setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_COMMANDS, false);
		end;
		
	OnCountFunds:
		setcurrency(#FISHPOINTS);
		end;

	OnPayFunds:
		if( #FISHPOINTS < @price ) {
			end;
		} else {
			#FISHPOINTS = #FISHPOINTS - @price;
			purchaseok(); 
		}
		end;
	
	OnInit:
		.npcName$ = "[^880088 "+strnpcinfo(1)+" ^000000]";
		.worm_id = 40117;
		setarray(.fish_id[0],40113,40114,40115);
		setarray(.rod_id[0],40118,40119,40120);
		tradertype(NST_CUSTOM);
		sellitem 12257,1;
		sellitem 44155,300;
		sellitem 44157,30;
		sellitem 44158,30;
		sellitem 44160,2;
		sellitem 44159,3;
		end;

	OnPCLoadMapEvent:
		if(strcharinfo(3) == strnpcinfo(4)) {
			.@questInProgress = 1;
			showevent 0,0;
			showevent(.@questInProgress ? QTYPE_QUEST : QTYPE_NONE);
		}
	end;
}

lasagna,234,302,1	script	Fishing Spot	4_CREEPER,{
	
	addtimer 1000, strnpcinfo(0)+"::On_Leave";
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_COMMANDS, true);
	mes .npcName$;
	mes "This area seems like a good spot for fishing..";
	menu "Cast your rod.",-;
	next;
	mes .npcName$;
	if(gettimetick(2) > fishing_license) {
		mes "You don't have a fishing license!";
		close;
	}
	if(countitem(.worm_id) < 1) {
		mes "You don't have a fishing bait!";
		close;
	}
	for(.@x = 0; .@x < getarraysize(.rod_id); .@x++) {
		if(getequipid(3) == .rod_id[.@x]) {
			if(FishLvl < .@x) { //Safety measure, in case user was able to use a rod beyond their level.
				mes "You don't have enough Fishing experience to cast this rod!";
				mes "Current Level: ^00AA00"+FishLvl+"^000000";
				mes "Required Level: ^AA0000"+getarrayindex(.rod_id[.@x])+"^000000";
				close;
			}
			.@rod_used = .@x+1;
			break;
		}
	}
	if(.@rod_used < 1) {
		mes "You don't have a fishing rod equipped!";
		close;
	}
	//if(.spot_taken) {
	//	mes "Someone's already fishing in this spot, find a new slant!";
	//	close;
	//} else {
	//	.spot_taken = 1;
	//	doevent strnpcinfo(3)+"::OnOccupy";
	//}

	switch(.@rod_used) {
		case 1:
			.@break_chance = 5;
			break;
		case 2:
			.@break_chance = 3;
			break;
		case 3:
			.@break_chance = 1;
			break;
		default: // Unreachable statement.
			break;
	}
	mes "Pwwiiiiiiiiisssssshhhhhhhh!!!";
	delitem .worm_id,1;
	cutin "Fishing_01",1;
	sleep2 200;
	cutin "Fishing_02",1;
	sleep2 200;
	cutin "Fishing_03",1;
	sleep2 200;
	cutin "Fishing_04",1;
	sleep2 200;
	cutin "Fishing_05",1;
	specialeffect 121,AREA,getcharid(3);
	.@fish_delay = 9-.@rod_used-FishLvl; //Reduce delay based on fishing level and rod used.
	.@frame_delay = .@fish_delay * 700 / 7; //Divide total delay by number of frames (7 frames from 06-12)
	cutin "Fishing_06",1;
	sleep2 .@frame_delay;
	cutin "Fishing_07",1;
	sleep2 .@frame_delay;
	cutin "Fishing_08",1;
	sleep2 .@frame_delay;
	cutin "Fishing_09",1;
	sleep2 .@frame_delay;
	cutin "Fishing_10",1;
	sleep2 .@frame_delay;
	cutin "Fishing_11",1;
	sleep2 .@frame_delay;
	cutin "Fishing_12",1;
	sleep2 .@frame_delay;
	specialeffect 227,AREA,getcharid(3);
	
	switch(FishLvl) { //Define success and chance to spawn monster based on fishing level.
		case 0:
			.@success_chance = 10;
			.@fish_monster = 60;
			break;
		case 1:
			.@success_chance = 20;
			.@fish_monster = 50;
			break;
		case 2:
			.@success_chance = 30;
			.@fish_monster = 40;
			break;
		case 3:
			.@success_chance = 40;
			.@fish_monster = 30;
			break;
		default: //Unreachable.
			break;
	}
	
	if(FishSuccessStreak == 3) { //Spawn a monster on 3 consecutive successes
		.@fish_streak = -5;
		FishSuccessStreak = 0;
	} else if(FishFailStreak == 3) { //Increase success chance on 3 consecutive fails
		.@fish_streak = 10;
		FishFailStreak = 0;
	}
	
	if(rand(100) < .@success_chance+(.@fish_streak)) { //Logic if the fishing attempt will success/fail
		FishSuccessStreak++;
		cutin "Fishing_13",1;
		.@fish_exp = 2; //Give 2 fishing exp for every success
		specialeffect 610,AREA,getcharid(3);
		switch(.@rod_used) { //Logic on what fish to give, depending on rod used.
			case 1:
				getitem .fish_id[.@rod_used-1],1;
				break;
			case 2:
				if(rand(2) == 1) {
					getitem .fish_id[.@rod_used-1],1;
				} else {
					getitem .fish_id[.@rod_used-2],1;
				}
				break;
			case 3:
				.@fish_chance = rand(10);
				if(.@fish_chance < 5) {
					getitem .fish_id[.@rod_used-1],1;
				} else if(.@fish_chance > 4 && .@fish_chance < 8) {
					getitem .fish_id[.@rod_used-2],1;
				} else {
					getitem .fish_id[.@rod_used-3],1;
				}
				break;
			default: //Unreachable statement
				break;
		}
		sleep2 2000;
		cutin("", 255);
		close2;
	} else {
		FishFailStreak++;
		cutin "Fishing_14",1;
		.@fish_exp = 1; //Give 1 fishing exp for every fail
		specialeffect 611,AREA,getcharid(3);
		if(rand(100) < .@fish_monster) {
			if((Hp*2) < MaxHp) { //Kill, if player ignores the monster 3x
				percentheal -100,-100;
			} else {
				percentheal -50,-50;
			}
			getmapxy(.@fmap$,.@fx,.@fy,UNITTYPE_PC);
			switch(rand(10)) { //Spawn different monsters
				case 0:
					.@fish_mob = 2069;
					break;
				case 1:
					.@fish_mob = 1044;
					break;
				case 2:
					.@fish_mob = 2070;
					break;
				case 3:
					.@fish_mob = 1069;
					break;
				case 4:
					.@fish_mob = 1141;
					break;
				case 5:
					.@fish_mob = 1066;
					break;
				case 6:
					.@fish_mob = 1144;
					break;
				case 7:
					.@fish_mob = 1264;
					break;
				case 8:
					.@fish_mob = 1045;
					break;
				default:
					.@fish_mob = 1065;
					break;
			}
			//.@fishgid = monster(.@fmap$,.@fx,.@fy,"--ja--",.@fish_mob,1,"Fishing Spot::OnShokoyKill");
			.@fishgid = monster(.@fmap$,.@fx,.@fy,"--ja--",.@fish_mob,1);
		}
		sleep2 2000;
		cutin("", 255);
		close2;
	}
	
	if(FishExp+.@fish_exp <= .fish_lvl[2]+1) {	
		FishExp+=.@fish_exp;
		if((FishLvl == 0 && (FishExp == .fish_lvl[0] || FishExp == .fish_lvl[0]+1)) || 
			(FishLvl == 1 && (FishExp == .fish_lvl[1] || FishExp == .fish_lvl[1]+1)) || 
			(FishLvl == 2 && (FishExp == .fish_lvl[2] || FishExp == .fish_lvl[2]+1))) { 
			if(FishLvl < 3) {
				FishLvl++;
			}
			announce strcharinfo(0)+" has leveled "+(Sex ? "his" : "her")+" Fishing Mastery to "+FishLvl+"!",bc_all|bc_blue;
		}
		message strcharinfo(0), "Fishing "+(.@fish_exp == 1 ? "failed" : "successful")+"! You gained "+.@fish_exp+" Fishing Exp. (Total: "+FishExp+" | Level: "+FishLvl+")";
	} else { //Stop giving exp once max level has been reached.
		message strcharinfo(0), "Fishing successful! You've already maxed your Fishing Mastery!";
	}
	
	if(rand(1000) < .@break_chance) { //Logic if the rod should break.
		delitem .rod_id[.@rod_used-1],1;
		message strcharinfo(0), "Oh no, your "+getitemname(.rod_id[.@rod_used-1])+" broke!";
	}
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_COMMANDS, false);
	if(.@fishgid) {
		if(.@fish_mob == 2069) {
			message strcharinfo(0),"A wild Sirena has appeared, kill it to get additional fishing experience!";
		} else {
			message strcharinfo(0),"A wild sea creature drained your health, kill it to restore your health!";
		}
		sleep2 30000;
		//killmonstergid .@fishgid;
	}
	end;
	
	On_Leave:
		setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_COMMANDS, false);
		end;
	
	//OnOccupy:
	//	for(.@y = 0; .@y < .@fish_delay; .@y++) {
	//		sleep2 1000;
	//	}
	//	.spot_taken = 0;
	//	end;
	
	OnInit:
		.npcName$ = "[^000088 "+strnpcinfo(1)+" ^000000]";
		.worm_id = 40117;
		setarray(.rod_id[0],40118,40119,40120);
		setarray(.fish_id[0],40113,40114,40115);
		setarray(.fish_lvl[0],10000,20000,40000); //Exp required per level.
		//.exp_lost = 69; //Lose 69 exp per death on fishing area.
		while(1) { //For consideration, remove it server lags.
			emotion e_gasp;
			.@sd = rand(30,60);
			for(.@s = 0; .@s < .@sd; .@s++) {
				sleep2 1000;
			}
		}
		end;
	
}

lasagna,234,312,1	duplicate(Fishing Spot)	Fishing Spot#1	4_CREEPER
lasagna,234,307,1	duplicate(Fishing Spot)	Fishing Spot#2	4_CREEPER
lasagna,234,297,1	duplicate(Fishing Spot)	Fishing Spot#3	4_CREEPER
lasagna,234,292,1	duplicate(Fishing Spot)	Fishing Spot#4	4_CREEPER
lasagna,229,314,1	duplicate(Fishing Spot)	Fishing Spot#5	4_CREEPER
lasagna,224,314,1	duplicate(Fishing Spot)	Fishing Spot#6	4_CREEPER
lasagna,219,314,1	duplicate(Fishing Spot)	Fishing Spot#7	4_CREEPER
lasagna,214,314,1	duplicate(Fishing Spot)	Fishing Spot#8	4_CREEPER
lasagna,214,319,1	duplicate(Fishing Spot)	Fishing Spot#9	4_CREEPER
lasagna,214,324,1	duplicate(Fishing Spot)	Fishing Spot#10	4_CREEPER

-	script	fish_func	-1,{
	
	OnInit:
		bindatcmd "fish",strnpcinfo(0)+"::OnAtcommand",0,99;
		.exp_lost = 69; //Lose 69 exp per death on fishing area.
		end;
		
	OnAtcommand:
		if (getmapflag(strcharinfo(3),mf_pvp) || getmapflag(strcharinfo(3),mf_gvg) || getmapflag(strcharinfo(3),mf_battleground)) {
			message strcharinfo(0),"@fish failed. You can't use this command in pvp/gvg maps.";
		} else {
			message strcharinfo(0),"Warping to Lasagna fishport.";
			specialeffect2 EF_BEGINSPELL7;
			progressbar "0x00ff00",1;
			specialeffect2 EF_JUMPBODY;
			sleep2 1000;
			warp "lasagna",224,274;
			sleep2 1000;
		}
		end;

	OnPCLoadMapEvent:
		if(strcharinfo(3) != "lasagna" && @worm_hunter) {
			undisguise();
			@worm_hunter = 0;
		}
		end;
		
	OnPCDieEvent:
		if(strcharinfo(3) == "lasagna") {
			if(FishExp >= .exp_lost) {
				FishExp-=.exp_lost;
				message strcharinfo(0),"You've been killed by sea creatures, you lost "+.exp_lost+" Fishing Exp. (Total: "+FishExp+" | Level: "+FishLvl+")";
			}
		}
		end;
	
	OnNPCKillEvent:
		if(strcharinfo(3) == "lasagna" && killedrid != 1127) {
			goto OnShokoyKill;
		}
		end;
	
	OnShokoyKill:
		if(killedrid == 2069) {
			if(FishExp < 20000) {
				FishExp++;
				message strcharinfo(0),"You gained 1 Fishing Exp. for killing the Sirena! (Total: "+FishExp+" | Level: "+FishLvl+")";
			} else {
				percentheal(50,50);
				message strcharinfo(0),"You've already maxed your Fishing Mastery, you got healed instead!";
			}
		} else {
			percentheal(50,50);
			message strcharinfo(0),"You've restored your health by killing the sea creature!";
		}
		end;
}

lasagna	mapflag	pvp	off
lasagna	mapflag	nobranch
lasagna	mapflag	nomemo
lasagna	mapflag	nopenalty
lasagna	mapflag	noteleport
lasagna	mapflag	nowarp
lasagna	mapflag	noicewall
lasagna	mapflag	noskill
lasagna	mapflag	noexp
lasagna	mapflag	novending
lasagna	mapflag	noloot
lasagna	mapflag	loadevent