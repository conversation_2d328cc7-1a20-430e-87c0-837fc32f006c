//===== <PERSON>ript ======================================
//= Euphy's Quest Shop
//===== By: ==================================================
//= Euphy
//===== Current Version: =====================================
//= 1.7
//===== Description: =========================================
//= A dynamic quest shop based on Lunar's, with easier config.
//= Includes support for multiple shops & cashpoints.
//= Item Preview script by ToastOfDoom.
//===== Additional Comments: =================================
//= 1.0 Initial script.
//= 1.2 Added category support.
//= 1.3 More options and fixes.
//= 1.4 Added debug settings.
//= 1.5 Replaced categories with shop IDs.
//= 1.6 Added support for purchasing stackables.
//= 1.6a Added support for previewing costumes and robes.
//= 1.7 Added bounded requirement checking.
//============================================================

// Shop NPCs -- supplying no argument displays entire menu.
//	callfunc "t3shop"{,<shop ID>{,<shop ID>{,...}}};
//============================================================
-	script	Old Tier	-1,{
	goto OnTalk;

OnTalk:
	mes .header$;
	mes "I can tier up your equipment to its fullest potential..";
	mes " ";
	mes "What kind of equipment do you want to upgrade?";
	callfunc "t3shop"; 
	end;

OnInit:
	.header$ = "[ Tier Upgrader ]";
	end;
}


// Script Core
//============================================================
-	script	t3quest_shop	FAKE_NPC,{
OnWhisperGlobal:
		if ( getgmlevel() < 90 ) end;
function Add; function Chk; function Slot; function A_An;
function IsTierReq; function GetTierName; function IsBoundRental;
OnInit:
	freeloop(1);

// -----------------------------------------------------------
//  Basic shop settings.
// -----------------------------------------------------------

	set .announce,1;	// Announce quest completion? (1: yes / 0: no)
	set .ShowSlot,1;	// Show item slots? (2: all equipment / 1: if slots > 0 / 0: never)
	set .ShowID,0;  	// Show item IDs? (1: yes / 0: no)
	set .ShowZeny,0;	// Show Zeny cost, if any? (1: yes / 0: no)
	set .MaxStack,100;	// Max number of quest items purchased at one time.
	set .TierSys, 1;    // Remove requirements excluding then item you want to upgrade. (1: yes | 0 : no)
	set .ReturnItemData, 1;

// -----------------------------------------------------------
//  Points variable -- optional quest requirement.
//	setarray .Points$[0],"<variable name>","<display name>";
// -----------------------------------------------------------

	setarray .Points$[0],"#arenapoints","Arena Points";

// -----------------------------------------------------------
//  Shop IDs -- to add shops, copy dummy data at bottom of file.
//	setarray .Shops$[1],"<Shop 1>","<Shop 2>"{,...};
// -----------------------------------------------------------

	setarray .Shops$[1],"Wings","Headgears","Accessories","Weapons","Boarding Halter - Mount","Artifacts I","Artifacts II","Artifacts III";

// -----------------------------------------------------------
//  Quest items -- do NOT use a reward item more than once!
//	Add(<shop ID>,<reward ID>,<reward amount>,
//	    <Zeny cost>,<point cost>,<chance>,
//	    <required item ID>,<required item amount>{,...});
//  Note : Make sure to put the equip to upgrade as a
//         first requirement.
// -----------------------------------------------------------

    // Add(4,TierItemID,Type,Chance,ReqZeny,MainReqId,ReqItemID,ReqItemAmount,,,);
    Add(1,20711,1,300000000,0,100,20124,1,4405,30,4290,30,4146,30,978,30,981,30,982,30,7088,6,3186,500,969,3000,3100,300);
    Add(1,3879,1,20000000,0,100,3008,2,3504,2,5102,2,1058,600,7101,500,924,400,3110,300,4172,10,4222,10,4173,10,976,30,980,30,979,30,969,3000,3100,100);
    Add(1,3878,1,20000000,0,100,3006,1,3002,1,3009,1,7168,400,3110,300,7053,200,7063,100,4213,10,4181,10,979,30,982,30,983,30,969,3000,3100,100);
    Add(1,20089,1,20000000,0,100,3005,1,3677,1,3660,1,3501,2,7214,300,7063,300,7067,300,969,3000,3100,100);
	Add(1,30210,1,20000000,0,100,3007,2,3010,2,3843,2,3740,2,3504,1,1029,300,12020,300,980,30,969,3000,3100,100);
	Add(1,20709,1,20000000,0,100,3660,1,7116,500,963,500,4131,10,978,20,969,3000,3100,100);

    Add(2,8140,1,100000000,0,100,3662,1,3007,1,4058,4,4121,4,4198,4,750,100,7297,100,7082,7,983,30,3100,100,969,3000);
	Add(2,8141,1,100000000,0,100,3659,1,3005,1,4058,4,4121,4,4198,4,10020,50,7023,100,7084,7,975,30,3100,100,969,3000);
	Add(2,8142,1,100000000,0,100,3017,1,3005,1,4058,4,4121,4,4198,4,751,100,7292,100,7080,7,982,30,3100,100,969,3000);
	Add(2,8143,1,100000000,0,100,3018,1,3008,1,4058,4,4121,4,4198,4,5102,1,7295,100,7081,7,983,30,3100,100,969,3000);
	Add(2,20710,1,100000000,0,100,3677,1,3013,1,7095,500,7116,500,963,500,978,20,4131,10,969,3000,3100,100);
	Add(2,30067,1,300000000,0,100,5518,1,5495,1,3663,1,3015,1,3009,1,752,100,7293,100,7081,7,982,30,3186,300,3100,100,969,3000);
    Add(2,20714,1,300000000,0,100,5013,1,6224,1,4419,4,4474,4,4425,4,4374,4,4198,4,8638,500,3186,500,969,6000,3100,600);
    Add(2,20165,1,300000000,0,100,3662,1,8140,1,3016,1,3843,1,4147,9,750,100,7291,100,7082,7,983,30,3186,300,3100,100,969,6000);
	Add(2,20700,1,300000000,0,100,3659,1,8141,1,3005,1,4419,8,4148,8,4198,8,10020,50,7023,100,7084,7,975,30,3186,300,3100,100,969,6000);
	Add(2,20701,1,300000000,0,100,3017,1,8142,1,3005,1,4058,50,4374,8,4198,8,751,100,7292,100,7080,7,982,30,3186,300,3100,100,969,6000);
	Add(2,20702,1,300000000,0,100,3018,1,8143,1,3008,1,4419,8,4411,50,4198,8,5102,2,7295,100,7081,7,983,30,3186,300,3100,100,969,6000);

    Add(3,3739,1,20000000,0,100,3294,2,7291,20,7214,500,7067,100,7053,300,12075,2,4008,2,969,100);
	Add(3,3742,1,20000000,0,100,3298,2,7290,20,7064,500,7101,100,924,300,12095,2,4094,2,969,100);
	Add(3,3740,1,20000000,0,100,3296,2,7450,20,7063,500,1003,100,1059,300,12085,2,4022,2,969,100);
	Add(3,3741,1,20000000,0,100,3297,2,7020,20,7441,500,7048,100,1058,300,12080,2,4279,2,969,100);
	Add(3,3743,1,20000000,0,100,3299,2,7513,20,7445,500,7027,100,7026,300,3110,300,12100,2,4006,2,969,100);
	Add(3,8768,1,300000000,0,100,8767,2,7291,20,7214,500,7067,100,7053,300,12075,2,4419,4,4008,4,969,3000,3100,300);
	Add(3,8761,1,20000000,0,100,3739,2,7291,20,7214,500,7067,100,7053,300,12075,2,4008,2,969,100);
	Add(3,8762,1,20000000,0,100,3742,2,7290,20,7064,500,7101,100,924,300,12095,2,4094,2,969,100);
	Add(3,8763,1,20000000,0,100,3740,2,7450,20,7063,500,1003,100,1059,300,12085,2,4022,2,969,100);
	Add(3,8764,1,20000000,0,100,3741,2,7020,20,7441,500,7048,100,1058,300,12080,2,4279,2,969,100);
	Add(3,8765,1,20000000,0,100,3743,2,7513,20,7445,500,7027,100,7026,300,3110,300,12100,2,4006,2,969,100);
	Add(3,20166,1,300000000,0,100,8767,2,8768,2,7291,40,7214,1000,7067,200,7053,600,12075,4,4419,8,4008,8,969,6000,3100,600);
    Add(3,20703,1,100000000,0,100,8761,2,7291,20,7214,500,7067,100,7053,300,12075,2,4008,5,969,300,3100,100);
	Add(3,20704,1,100000000,0,100,8762,2,7290,20,7064,500,7101,100,924,300,12095,2,4094,5,969,300,3100,100);
	Add(3,20705,1,100000000,0,100,8763,2,7450,20,7063,500,1003,100,1059,300,12085,2,4022,5,969,300,3100,100);
	Add(3,20706,1,100000000,0,100,8764,2,7020,20,7441,500,7048,100,1058,300,12080,2,4279,5,969,300,3100,100);
	Add(3,20707,1,100000000,0,100,8765,2,7513,20,7445,500,7027,100,7026,300,3110,300,12100,2,4006,5,969,300,3100,100);
	Add(3,20708,1,300000000,0,100,20166,2,7291,40,7214,1000,7067,200,7053,600,4419,10,6226,1,3186,500,969,6000,3100,600);

    Add(4,8247,1,20000000,0,100,1237,1,7023,50,12020,50,7511,50,3133,3,4133,3,4174,3,4047,3,1237,3,969,3000,3100,300);
	Add(4,8246,1,20000000,0,100,1228,1,7023,50,12020,50,7511,50,3133,3,4058,3,4168,3,4169,3,1228,1,969,3000,3100,300);
	Add(4,8766,1,20000000,0,100,1161,1,7079,6,7089,6,7088,6,3500,1,1135,1,1133,1,1131,1,1141,1,1136,1,969,3000,3100,300);
    Add(4,30068,1,300000000,0,100,3504,2,3003,1,3002,1,7079,6,7089,6,7088,6,3529,2,3186,400,969,6000,3100,300);
    Add(4,20713,1,300000000,0,100,8247,1,6224,1,7023,50,12020,50,3133,30,4174,6,4047,6,8638,500,3186,500,969,6000,3100,600);
	Add(4,20712,1,300000000,0,100,8246,1,6224,1,7023,50,12020,50,3133,30,4168,6,4169,6,8638,500,3186,500,969,6000,3100,600);
    Add(4,20167,1,300000000,0,100,1161,1,8766,1,7083,6,7084,6,7085,6,3500,6,7023,400,3186,400,969,6000,3100,300);

	Add(5,12623,1,300000000,30000,100,12622,1,6224,3,7773,3000,3191,333,8638,333,3186,333,7293,33,7443,33,3133,333,3187,100);
	Add(5,12624,1,300000000,60000,100,12623,1,6224,5,7773,3000,3191,666,8638,666,3186,666,7293,66,7443,66,3133,666,7086,20,3187,200);
	Add(5,12625,1,300000000,90000,100,12624,1,6224,7,7773,3000,3191,999,8638,999,3186,999,7293,99,7443,99,3133,999,7086,30,3187,300);

	Add(6,44516,1,300000000,30000,60,44500,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44517,1,300000000,30000,60,44501,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44518,1,300000000,30000,60,44502,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44519,1,300000000,30000,60,44503,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44520,1,300000000,30000,60,44504,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44521,1,300000000,30000,60,44505,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44522,1,300000000,30000,60,44506,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44523,1,300000000,30000,60,44507,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44524,1,300000000,30000,60,44508,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44525,1,300000000,30000,60,44509,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44526,1,300000000,30000,60,44510,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44527,1,300000000,30000,60,44511,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44528,1,300000000,30000,60,44512,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44529,1,300000000,30000,60,44513,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44530,1,300000000,30000,60,44514,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);
	Add(6,44531,1,300000000,30000,60,44515,1,3142,10,7086,10,3191,300,40112,10,40111,10,3186,300,8638,300,3133,300,7773,3000,3187,100);

	Add(7,44532,1,300000000,30000,55,44516,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44533,1,300000000,30000,55,44517,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44534,1,300000000,30000,55,44518,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44535,1,300000000,30000,55,44519,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44536,1,300000000,30000,55,44520,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44537,1,300000000,30000,55,44521,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44538,1,300000000,30000,55,44522,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44539,1,300000000,30000,55,44523,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44540,1,300000000,30000,55,44524,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44541,1,300000000,30000,55,44525,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44542,1,300000000,30000,55,44526,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44543,1,300000000,30000,55,44527,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44544,1,300000000,30000,55,44528,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44545,1,300000000,30000,55,44529,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44546,1,300000000,30000,55,44530,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);
	Add(7,44547,1,300000000,30000,55,44531,1,3142,20,7086,20,3191,600,40112,20,40111,20,3186,600,8638,600,3133,600,7773,3000,3187,200);

	Add(8,44548,1,300000000,30000,50,44532,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44549,1,300000000,30000,50,44533,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44550,1,300000000,30000,50,44534,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44551,1,300000000,30000,50,44535,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44552,1,300000000,30000,50,44536,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44553,1,300000000,30000,50,44537,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44554,1,300000000,30000,50,44538,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44555,1,300000000,30000,50,44539,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44556,1,300000000,30000,50,44540,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44557,1,300000000,30000,50,44541,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44558,1,300000000,30000,50,44542,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44559,1,300000000,30000,50,44543,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44560,1,300000000,30000,50,44544,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44561,1,300000000,30000,50,44545,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44562,1,300000000,30000,50,44546,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);
	Add(8,44563,1,300000000,30000,50,44547,1,3142,30,7086,30,3191,900,40112,30,40111,30,3186,900,8638,900,3133,900,7773,3000,3187,300);

// -----------------------------------------------------------

	freeloop(0);
	set .menu$,"";
	for(set .@i,1; .@i<=getarraysize(.Shops$); set .@i,.@i+1) {
		set .menu$, .menu$+.Shops$[.@i]+":";
		npcshopdelitem "t3shop"+.@i,909;
	}
	end;

OnMenu:
	set .@size, getarraysize(@i);
	if (!.@size) set .@i, select(.menu$);
	else if (.@size == 1) set .@i, @i[0];
	else {
		for(set .@j,0; .@j<.@size; set .@j,.@j+1)
			set .@menu$, .@menu$+.Shops$[@i[.@j]]+":";
		set .@i, @i[select(.@menu$)-1];
	}
	deletearray @i[0],getarraysize(@i);
	if (.Shops$[.@i] == "") {
		message strcharinfo(PC_NAME),"An error has occurred.";
		end;
	}
	dispbottom "Select one item at a time.";
	@shopee = .@i;
	callshop "t3shop"+.@i,1;
	npcshopattach "t3shop"+.@i;
	end;

OnBuyItem:
	disable_items();
	getinventorylist();
	// .@q[] : RewardID, BoughtAmt, RewardAmt, BaseAmt, ReqZeny, ReqPts, Chance, { ReqItem, ReqAmt, ... }
	setarray .@q[0],@bought_nameid[0],((@bought_quantity[0] > .MaxStack)?.MaxStack:@bought_quantity[0]);
	copyarray .@q[3],getd(".q_"+.@q[0]+"[0]"),getarraysize(getd(".q_"+.@q[0]));
	set .@q[2],.@q[1]*.@q[3];
	if (!.@q[2] || .@q[2] > 30000) {
		message strcharinfo(PC_NAME),"You can't purchase that many "+getitemname(.@q[0])+".";
		end;
	}
	deletearray(@bound_items);
	deletearray(@rental_items);
	for (.@i=0; .@i<@inventorylist_count; .@i++) {
		if (@inventorylist_expire[.@i] > 0)
			setarray @rental_items[getarraysize(@rental_items)], @inventorylist_id[.@i];
		if (@inventorylist_bound[.@i] > 0)
			setarray @bound_items[getarraysize(@bound_items)], @inventorylist_id[.@i];
	}
	mes "[ Tier Upgrader ]";
	mesf("Reward: %s", F_MesItemInfo(.@q[0]));
	mesf("Success Rate: ^009900%d%%^000000", .@q[6]);
	mes "Requirements:";
	if (.@q[4]) mesf("%s ^3355FF%s Zeny^000000", Chk(Zeny, .@q[4]*.@q[1]), F_InsertComma(.@q[4]*.@q[1]));
	if (.@q[5]) {
		set .@points, getd(.Points$[0]);
		mesf("%s ^3355FF%s %s^000000", Chk(.@points, .@q[5]*.@q[1]), F_InsertComma(.@q[5]*.@q[1]), .Points$[1]);
	}
	if (.@q[7]) {
		for(set .@i,7; .@i<getarraysize(.@q); set .@i,.@i+2)
			mesf("%s ^3355FF%d^000000 %s", Chk(countitem(.@q[.@i]),.@q[.@i+1]*.@q[1],.@q[.@i]), (.@q[.@i+1]*.@q[1]), F_MesItemInfo(.@q[.@i]));
	}
	next;
	if (.TierSys) {
		mes "[ Tier Upgrader ]";
		mes "Just a reminder,";
		mes "If the upgrade chance is not 100% and fails, all the requirements";
		mes "will be consumed, except";
		mesf("the %s.", F_MesItemInfo(.@q[7]));
		next;
	}
	setarray @qe[1], getiteminfo(.@q[0], ITEMINFO_LOC), getiteminfo(.@q[0], ITEMINFO_VIEWSPRITE);
	if (@qe[2] > 0 && ((@qe[1] & EQP_HEAD_LOW) || (@qe[1] & EQP_HEAD_TOP) || (@qe[1] & EQP_HEAD_MID) || (@qe[1] & EQP_COSTUME_HEAD_TOP) || (@qe[1] & EQP_COSTUME_HEAD_MID) || (@qe[1] & EQP_COSTUME_HEAD_LOW) || (@qe[1] & EQP_GARMENT) || (@qe[1] & EQP_COSTUME_GARMENT)))
		set .@preview,1;
	addtimer 1000, strnpcinfo(NPC_NAME)+"::OnEnd";
	while(1) {
		switch(select("Create ^0055FF"+ getitemname(.@q[0]) +"^000000", ((.@preview && !@qe[7])?"Preview...": ""), "^777777Cancel^000000")) {
		case 1:
			if (@qe[0]) {
				mes "[ Tier Upgrader ]";
				mes "You're missing one or more quest requirements.";
				close;
			}
			if (!checkweight(.@q[0],.@q[2])) {
				mes "[ Tier Upgrader ]";
				mes "^FF0000You need "+(((.@q[2] * getiteminfo(.@q[0], ITEMINFO_WEIGHT)) + Weight - MaxWeight) / 10)+" additional weight capacity to complete this trade.^000000";
				close;
			}
			if (!isequipped(.@q[7]) && @shopee != 5)
			{
				mes "[ Tier Upgrader ]";
				mesf("You should equip %s that you want to upgrade", F_MesItemInfo(.@q[7]));
				mes "^FF0000NOTE: Refine and Card will be transfered.^000000";
				close;
			}
			if (.@q[4]) Zeny -= (.@q[4]*.@q[1]);
			if (.@q[5]) setd .Points$[0], getd(.Points$[0])-(.@q[5]*.@q[1]);
			if (.TierSys && rand(100) < .@q[6]) {
				if (.ReturnItemData)
				{
					getinventorylist();
					freeloop(1);
					.@eqi = -1;
					for (.@i = 0; .@i < @inventorylist_count; .@i++)
					{
						if (@inventorylist_id[.@i] != .@q[7])
							continue;
						if (@inventorylist_amount[.@i] < .@q[8])
							continue;
						if (@inventorylist_equip[.@i] == 0)
							continue;
						else if (@inventorylist_equip[.@i] & EQP_HEAD_TOP)
							.@eqi = EQI_HEAD_TOP;
						else if (@inventorylist_equip[.@i] & EQP_HEAD_MID)
							.@eqi = EQI_HEAD_MID;
						else if (@inventorylist_equip[.@i] & EQP_HEAD_LOW)
							.@eqi = EQI_HEAD_LOW;
						else if (@inventorylist_equip[.@i] & EQP_ARMOR)
							.@eqi = EQI_ARMOR;
						else if (@inventorylist_equip[.@i] & EQP_HAND_L)
							.@eqi = EQI_HAND_L;
						else if (@inventorylist_equip[.@i] & EQP_HAND_R)
							.@eqi = EQI_HAND_R;
						else if (@inventorylist_equip[.@i] & EQP_GARMENT)
							.@eqi = EQI_GARMENT;
						else if (@inventorylist_equip[.@i] & EQP_SHOES)
							.@eqi = EQI_SHOES;
						else if (@inventorylist_equip[.@i] & EQP_ACC_L)
							.@eqi = EQI_ACC_L;
						else if (@inventorylist_equip[.@i] & EQP_ACC_R)
							.@eqi = EQI_ACC_R;
						else if (@inventorylist_equip[.@i] & EQP_COSTUME_HEAD_TOP)
							.@eqi = EQI_COSTUME_TOP;
						else if (@inventorylist_equip[.@i] & EQP_COSTUME_HEAD_MID)
							.@eqi = EQI_COSTUME_MID;
						else if (@inventorylist_equip[.@i] & EQP_COSTUME_HEAD_LOW)
							.@eqi = EQI_COSTUME_LOW;
						else if (@inventorylist_equip[.@i] & EQP_SHADOW_ARMOR)
							.@eqi = EQI_SHADOW_ARMOR;
						else if (@inventorylist_equip[.@i] & EQP_SHADOW_WEAPON)
							.@eqi = EQI_SHADOW_WEAPON;
						else if (@inventorylist_equip[.@i] & EQP_SHADOW_SHIELD)
							.@eqi = EQI_SHADOW_SHIELD;
						else if (@inventorylist_equip[.@i] & EQP_COSTUME_GARMENT)
							.@eqi = EQI_COSTUME_GARMENT;
						else if (@inventorylist_equip[.@i] & EQP_SHADOW_SHOES)
							.@eqi = EQI_SHADOW_SHOES;
						else if (@inventorylist_equip[.@i] & EQP_SHADOW_ACC_L)
							.@eqi = EQI_SHADOW_ACC_L;
						else if (@inventorylist_equip[.@i] & EQP_SHADOW_ACC_R)
							.@eqi = EQI_SHADOW_ACC_R;
						.@refine = @inventorylist_refine[.@i];
						for (.@k = 0; .@k < 4; .@k++)
							.@card[.@k] = getd(sprintf("@inventorylist_card%d[%d]", .@k+1, .@i));
						// Unles creating script command for like getitem3 in rAthena emu
						// or by equiping the item then use setequipoption
						// but if the user failed to equip the equip it will fail
						//for (.@k = 0; .@k < 5; .@k++)
						//{
						//	.@opt_index[.@k] = getd(sprintf("@inventorylist_opt_id%d[%d]", .@k+1, .@i));
						//	.@opt_value[.@k] = getd(sprintf("@inventorylist_opt_val%d[%d]", .@k+1, .@i));
						//	.@opt_param[.@k] = getd(sprintf("@inventorylist_opt_param%d[%d]", .@k+1, .@i));
						//}
						break;
					}
					freeloop(0);
					if (.@eqi != -1)
						delequip(.@eqi);
					else
						delitem(.@q[7], .@q[8]);
					getitem2(.@q[0], .@q[2], 1, .@refine, 0, .@card[0], .@card[1], .@card[2], .@card[3]);
				}
				else
				{
					delitem(.@q[7], .@q[8]);
					getitem .@q[0],.@q[2];
				}
				if (.announce) bcshout strcharinfo(PC_NAME)+" has created "+((.@q[2] > 1)?.@q[2]+"x "+getitemname(.@q[0]):A_An(getitemname(.@q[0])))+"!",bc_all,0x00FFFF;
				specialeffect(EF_REFINEOK, AREA, playerattached());
				mes "[ Tier Upgrader ]";
				mes "The upgrade is a success.";
				mes " ";
				mesf("Here is your %s", F_MesItemInfo(.@q[0]));
			} else {
				specialeffect(EF_REFINEFAIL, AREA, playerattached());
				mes "[ Tier Upgrader ]";
				mes "Unfortunately, the upgrade";
				mes "has failed.";
				mes " ";
				mes "But fret not...";
				mesf("Your %s is\r\nstill intact.", F_MesItemInfo(.@q[7]));
			}
			// delete last because it could remove main requirement
			if (.@q[7]) { 
				.@i = (.TierSys ? 9 : 7);
				while (.@i < getarraysize(.@q)) {
					delitem .@q[.@i],.@q[.@i+1]*.@q[1];
					.@i+=2;
				}
			}
			close;
		case 2:
			setarray @qe[3], getlook(LOOK_HEAD_BOTTOM), getlook(LOOK_HEAD_TOP), getlook(LOOK_HEAD_MID), getlook(LOOK_ROBE), 1;
			if ((@qe[1] & 1) || (@qe[1] & 4096)) changelook LOOK_HEAD_BOTTOM, @qe[2];
			else if ((@qe[1] & 256) || (@qe[1] & 1024)) changelook LOOK_HEAD_TOP, @qe[2];
			else if ((@qe[1] & 512) || (@qe[1] & 2048)) changelook LOOK_HEAD_MID, @qe[2];
			else if ((@qe[1] & 4) || (@qe[1] & 8192)) changelook LOOK_ROBE, @qe[2];
			break;
		case 3:
			close;
		}
	}

OnEnd:
	if (@qe[7]) {
		changelook LOOK_HEAD_BOTTOM, @qe[3];
		changelook LOOK_HEAD_TOP, @qe[4];
		changelook LOOK_HEAD_MID, @qe[5];
		changelook LOOK_ROBE, @qe[6];
	}
	deletearray @qe[0],8;
	deletearray @bound_items;
	deletearray @rental_items;
	end;

function Add {
	if (getitemname(getarg(1)) == "null") {
		//debugmes "Quest reward #"+getarg(1)+" invalid (skipped).";
		return;
	}
	setarray .@j[0],getarg(2),getarg(3),getarg(4),getarg(5);
	for(set .@i,6; .@i<getargcount(); set .@i,.@i+2) {
		if (getitemname(getarg(.@i)) == "null") {
			//debugmes "Quest requirement #"+getarg(.@i)+" invalid (skipped).";
			return;
		} else
			setarray .@j[.@i-2],getarg(.@i),getarg(.@i+1);
	}
	copyarray getd(".q_"+getarg(1)+"[0]"),.@j[0],getarraysize(.@j);
	npcshopadditem "t3shop"+getarg(0),getarg(1),((.ShowZeny)?getarg(3):0);
	return;
}

function Chk {
	if (getarraysize(@bound_items)) {
		for (.@i=0; .@i<getarraysize(@bound_items); .@i++) {
			if (getitemname(getarg(2,0)) == "null")
				continue;
			if (@bound_items[.@i] == getarg(2)) {
				set @qe[0], 1;
				return "^DD7700[B]^000000";
			}
		}
	}
	if (getarraysize(@rental_items)) {
		for (.@i=0; .@i<getarraysize(@rental_items); .@i++) {
			if (getitemname(getarg(2,0)) == "null")
				continue;
			if (@rental_items[.@i] == getarg(2)) {
				set @qe[0], 1;
				return "^990099[R]^000000";
			}
		}
	}
	if (getarg(0) < getarg(1)) {
		set @qe[0],1;
		return "^FF0000[X]^000000";
	} else
		return "^009900[O]^000000";
}

function Slot {
	set .@s$,getitemname(getarg(0));
	switch(.ShowSlot) {
		case 1: if (!getitemslots(getarg(0))) return .@s$;
		case 2: if (getiteminfo(getarg(0), ITEMINFO_TYPE) == IT_WEAPON || getiteminfo(getarg(0), ITEMINFO_TYPE) == IT_ARMOR) return .@s$+" ["+getitemslots(getarg(0))+"]";
		default: return .@s$;
	}
}

function A_An {
	setarray .@A$[0],"a","e","i","o","u";
	set .@B$, "_"+getarg(0);
	for(set .@i,0; .@i<5; set .@i,.@i+1)
		if (compare(.@B$,"_"+.@A$[.@i])) return "an "+getarg(0);
	return "a "+getarg(0);
}

function IsTierReq {
	.@part = getarg(0);
	.@type = getarg(1, 0);

	.@itemid = getequipid(.@part);

	// check if item is a MainReqId.
	freeloop(1);
	for (.@i = 1; .@i <= .t; .@i++) {
		if (getd(".MainReqId" + .@i) == .@itemid && (getd(".Type" + .@i) == .@type || .@type == 0)) {
			return true;
		}
	}
	freeloop(0);
	return false;

}

function GetTierName {
	.@part = getarg(0);

	.@itemid = getequipid(.@part);

	// check if item is a MainReqId.
	freeloop(1);
	for (.@i = 1; .@i <= .t; .@i++) {
		if (getd(".MainReqId" + .@i) == .@itemid) {
			return getitemname(getd(".TierItemID" + .@i));
		}
	}
	freeloop(0);
	return "";

}

function IsBoundRental {
	.@part = getarg(0);
	.@itemid = getequipid(.@part);
	
	
	getinventorylist();
	freeloop(1);
	for (.@i = 0; .@i < @inventorylist_count; .@i++) {
		if (@inventorylist_id[.@i] == .@itemid && (@inventorylist_bound[.@i] > 0 || @inventorylist_expire[.@i] > 0)) {
			return true;
		}
	}
	freeloop(0);

	return false;
}

}

function	script	t3shop	{
	deletearray @i[0],getarraysize(@i);
	for(set .@i,0; .@i<getargcount(); set .@i,.@i+1)
		set @i[.@i],getarg(.@i);
	doevent "t3quest_shop::OnMenu";
	end;
}




// Dummy shop data -- copy as needed.
//============================================================
-	shop	t3shop1	FAKE_NPC,909:-1
-	shop	t3shop2	FAKE_NPC,909:-1
-	shop	t3shop3	FAKE_NPC,909:-1
-	shop	t3shop4	FAKE_NPC,909:-1
-	shop	t3shop5	FAKE_NPC,909:-1
-	shop	t3shop6	FAKE_NPC,909:-1
-	shop	t3shop7	FAKE_NPC,909:-1
-	shop	t3shop8	FAKE_NPC,909:-1
-	shop	t3shop9	FAKE_NPC,909:-1