//===== Hercules Script ======================================
//= Euphy's Quest Shop
//===== By: ==================================================
//= Euphy
//===== Current Version: =====================================
//= 1.6a
//===== Description: =========================================
//= A dynamic quest shop based on Lunar's, with easier config.
//= Includes support for multiple shops & cashpoints.
//= Item Preview script by ToastOfDoom.
//===== Additional Comments: =================================
//= 1.0 Initial script.
//= 1.2 Added category support.
//= 1.3 More options and fixes.
//= 1.4 Added debug settings.
//= 1.5 Replaced categories with shop IDs.
//= 1.6 Added support for purchasing stackables.
//= 1.6a Added support for previewing costumes and robes.
//============================================================

// Shop NPCs -- supplying no argument displays entire menu.
//	callfunc "btshop"{,<shop ID>{,<shop ID>{,...}}};
//============================================================
function	script	IsRentalBound	{
	.@itemid = getarg(0);


	getinventorylist();
	freeloop(true);
	for (.@i = 0; .@i < @inventorylist_count; .@i++){
		if (@inventorylist_id[.@i] == .@itemid && (@inventorylist_expire[.@i] > 0 || @inventorylist_bound[.@i] > 0)) {
			mes "[ ^996600Quest Shop^000000 ]";
			mes "Sorry, you cannot use rental or bound items as a requirement.";
			close;
		}
	}
	freeloop(false);

	return;
		
}

prt_in2,102,184,3	script	BTS Quest Shop#1	4_F_HIMEL,{
	if (bts_limit >= 5) {
        mes "[ ^0000FFQuest Shop^000000 ]";
		mes "You've reached the maximum (5) quest per character.";
        mes " ";
        mes "You have to finish the Break the Seal Quest again in order to refresh your quest limit.";
        BTS_Q = 0;
		BTS_2Q = 0;
		close;
	}
    message strcharinfo(0),"This quest is limited. You have " +(5-bts_limit)+ " remaining quest(s) left.";
	callfunc "btshop";
	
	//OnPCLoadMapEvent:
	//	if(strcharinfo(3) == strnpcinfo(4)) {
	//		.@questInProgress = 1;
	//		showevent 0,0;
	//		showevent(.@questInProgress ? QTYPE_QUEST : QTYPE_NONE);
	//	}
	//end;
}


// Script Core
//============================================================
-	script	btsquest_shop	FAKE_NPC,{
OnWhisperGlobal:
		if ( getgmlevel() < 90 ) end;
function Add; function Chk; function Slot; function A_An;
OnInit:
	freeloop(1);

// -----------------------------------------------------------
//  Basic shop settings.
// -----------------------------------------------------------

	set .Announce,1;	// Announce quest completion? (1: yes / 0: no)
	set .ShowSlot,1;	// Show item slots? (2: all equipment / 1: if slots > 0 / 0: never)
	set .ShowID,0;  	// Show item IDs? (1: yes / 0: no)
	set .ShowZeny,0;	// Show Zeny cost, if any? (1: yes / 0: no)
	set .MaxStack,100;	// Max number of quest items purchased at one time.

// -----------------------------------------------------------
//  Points variable -- optional quest requirement.
//	setarray .Points$[0],"<variable name>","<display name>";
// -----------------------------------------------------------

	setarray .Points$[0],"#ACTIVITYPOINTS","Activity Points";

// -----------------------------------------------------------
//  Shop IDs -- to add shops, copy dummy data at bottom of file.
//	setarray .Shops$[1],"<Shop 1>","<Shop 2>"{,...};
// -----------------------------------------------------------

	setarray .Shops$[1],"BTS - Quest Items","BTS2 - Quest Items","BTS2 - Otherworld Set","BTS2 - Divine Set","BTS2 - Class Figures","BTS2 - Hydra Weapons";

// -----------------------------------------------------------
//  Quest items -- do NOT use a reward item more than once!
//	Add(<shop ID>,<reward ID>,<reward amount>,
//	    <Zeny cost>,<point cost>,
//	    <required item ID>,<required item amount>{,...});
// -----------------------------------------------------------

	Add(1,44154,1,300000000,300,44155,1,44156,1,3142,10,7086,10,40112,10,40111,10,3191,500,3186,500,8638,500,3133,500,7111,1000,3187,100);
	Add(1,3135,1,100000000,300,3100,300,3133,30,969,3000);
	Add(1,3144,1,200000000,300,6224,2,3142,3,3191,50,3186,200,3133,200,8638,200);
	Add(1,3162,1,300000000,333,3160,1,3161,1,3187,33,3100,333,3133,333,969,3333);
	Add(1,2647,1,300000000,30,7073,4,7091,4,3133,5,7211,30,7444,100,969,3000);
	Add(1,2357,1,300000000,30,7090,4,7077,4,3133,10,7443,100,7444,100,969,3000);
	Add(1,2524,1,300000000,30,7089,4,7086,4,3133,10,7035,50,7444,100,969,3000);
	Add(1,2115,1,300000000,30,7078,4,7088,4,3133,10,7451,50,7444,100,969,3000);
	Add(1,2421,1,300000000,30,7083,4,7092,4,3133,10,7450,20,7444,100,969,3000);
	Add(1,2410,1,300000000,30,918,500,7166,250,7079,4,7080,4,7086,4,7444,100,969,3000);
	Add(1,2646,1,300000000,30,7075,4,7087,4,3133,5,7211,15,7444,100,969,3000);
	Add(1,8767,1,300000000,20,8761,2,7291,20,7214,500,7067,100,7053,300,12075,2,4419,2,4008,2,969,3000,3100,300);
	
	Add(2,2554,1,100000000,20,6091,100,12114,50,12115,50,12116,50,12117,50,7091,7,7075,7,969,100,3100,100);
	Add(2,2423,1,100000000,20,6091,100,7451,100,7120,700,918,1300,7166,200,7440,500,7078,3,7079,3,7080,3,7086,3,7082,3,969,100,3100,100);
	Add(2,20722,1,300000000,30,2357,1,6224,1,6225,1,4419,8,7086,8,7074,8,7091,8,3133,50,6091,50,8638,500,3186,500,969,6000,3100,300);
	Add(2,20723,1,300000000,30,2115,1,6224,1,6225,1,4374,8,7086,8,7075,8,7092,8,3133,50,6091,50,8638,500,3186,500,969,6000,3100,300);
	Add(2,3192,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,6091,100,8880,30000,969,30000,3100,300);

	Add(3,30069,1,300000000,30,7090,8,7077,8,3133,20,3186,500,8638,300,7443,100,7444,300,969,6000,3100,100);
    Add(3,30070,1,300000000,30,7083,8,7092,8,3133,20,3186,500,8638,300,7450,100,7444,300,969,6000,3100,100);
	Add(3,30071,1,300000000,30,7089,8,7086,8,3133,20,3186,500,8638,300,7035,100,7444,300,969,6000,3100,100);
	Add(3,30072,1,300000000,30,7078,8,7088,8,3133,20,3186,500,8638,300,7451,100,7444,300,969,6000,3100,100);
	Add(3,30213,1,300000000,30,7086,8,7085,8,3133,20,3186,1000,8638,300,3191,100,7444,300,969,6000,3100,100);

	Add(4,40084,1,300000000,30,7090,8,7077,8,3133,100,3186,999,7757,888,8638,777,3191,666,969,6666,3100,333);
    Add(4,40085,1,300000000,30,7083,8,7092,8,3133,100,3186,999,7757,888,8638,777,3191,666,969,6666,3100,333);
	Add(4,40086,1,300000000,30,7089,8,7086,8,3133,100,3186,999,7757,888,8638,777,3191,666,969,6666,3100,333);
	Add(4,40087,1,300000000,30,7078,8,7088,8,3133,100,3186,999,7757,888,8638,777,3191,666,969,6666,3100,333);
	Add(4,40088,1,300000000,30,7086,8,7085,8,3133,100,3186,999,7757,888,8638,777,3191,666,969,6666,3100,333);

	Add(5,30214,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30215,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30216,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30217,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30218,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30219,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30220,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30221,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30222,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30223,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30224,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);
	Add(5,30420,1,300000000,50,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3100,300);

	Add(6,30463,1,300000000,50,30057,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30464,1,300000000,50,30049,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30465,1,300000000,50,30053,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30466,1,300000000,50,30051,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30467,1,300000000,50,30052,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30468,1,300000000,50,30047,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30469,1,300000000,50,30050,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30470,1,300000000,50,30055,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30471,1,300000000,50,30058,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30472,1,300000000,50,30046,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30473,1,300000000,50,30056,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30474,1,300000000,50,30048,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);
	Add(6,30475,1,300000000,50,30046,1,7086,32,3133,100,3186,1000,8638,1000,3191,500,7443,100,8880,30000,969,30000,3187,300);

// -----------------------------------------------------------

	freeloop(0);
	set .menu$,"";
	for(set .@i,1; .@i<=getarraysize(.Shops$); set .@i,.@i+1) {
		set .menu$, .menu$+.Shops$[.@i]+":";
		npcshopdelitem "btshop"+.@i,909;
	}
	end;
OnLeave:
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS, false);
	end;

OnMenu:
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS, true);
	addtimer 500, strnpcinfo(3)+"::OnLeave";
	//mes "[ ^996600Quest Shop^000000 ]";
	//mes ".";
	set .@size, getarraysize(@i);
	if (!.@size) set .@i, select(.menu$);
	else if (.@size == 1) set .@i, @i[0];
	else {
		for(set .@j,0; .@j<.@size; set .@j,.@j+1)
			set .@menu$, .@menu$+.Shops$[@i[.@j]]+":";
		set .@i, @i[select(.@menu$)-1];
	}
	deletearray @i[0],getarraysize(@i);
	if (.Shops$[.@i] == "") {
		message strcharinfo(PC_NAME),"An error has occurred.";
		end;
	}
	dispbottom "Select one item at a time.";
	@shopeee = .@i;
	callshop "btshop"+.@i,1;
	npcshopattach "btshop"+.@i;
	if (@shopeee==1) {
		mes "[ ^996600Quest Shop^000000 ]";
		mes " ";
		mes "You need to finish break the seal quest first to be able to do these quest. Goodluck!";
	}
	if (@shopeee==2) {
		mes "[ ^996600Quest Shop^000000 ]";
		mes " ";
		mes "You need to finish break the seal quest first to be able to do these quest. Goodluck!";
	}
    if (@shopeee==3) {
		mes "[ ^996600Quest Shop^000000 ]";
		mes " ";
		mes "You need to finish break the seal 2 quest first to be able to do these quest. Goodluck!";
	}
    if (@shopeee==4) {
		mes "[ ^996600Quest Shop^000000 ]";
		mes " ";
		mes "You need to finish break the seal 2 quest first to be able to do these quest. Goodluck!";
	}
	if (@shopeee==5) {
		mes "[ ^996600Quest Shop^000000 ]";
		mes " ";
		mes "You need to finish break the seal 2 quest first to be able to do these quest. Goodluck!";
	}
	if (@shopeee==6) {
		mes "[ ^996600Quest Shop^000000 ]";
		mes " ";
		mes "You need to finish break the seal 2 quest first to be able to do these quest. Goodluck!";
	}
	if (@shopeee==7) {
		mes "[ ^996600Quest Shop^000000 ]";
		mes " ";
		mes "You need to finish break the seal 2 quest first to be able to do these quest. Goodluck!";
	}
	end;

OnBuyItem:
	// .@q[] : RewardID, BoughtAmt, RewardAmt, BaseAmt, ReqZeny, ReqPts, { ReqItem, ReqAmt, ... }
	setarray .@q[0],@bought_nameid[0],((@bought_quantity[0] > .MaxStack)?.MaxStack:@bought_quantity[0]);
	copyarray .@q[3],getd(".q_"+.@q[0]+"[0]"),getarraysize(getd(".q_"+.@q[0]));
	set .@q[2],.@q[1]*.@q[3];
	if (!.@q[2] || .@q[2] > 30000) {
		message strcharinfo(PC_NAME),"You can't purchase that many "+getitemname(.@q[0])+".";
		end;
	}
	
	mes "[ ^996600Quest Shop^000000 ]";
	mes "Reward: ^0055FF"+((.@q[2] > 1)?.@q[2]+"x ":"")+Slot(.@q[0])+"^000000";
	mes "Requirements:";
	if (.@q[4]) mes " > "+Chk(Zeny,.@q[4]*.@q[1])+(.@q[4]*.@q[1])+" Zeny^000000";
	if (.@q[5]) mes " > "+Chk(getd(.Points$[0]),.@q[5]*.@q[1])+(.@q[5]*.@q[1])+" "+.Points$[1]+" ("+getd(.Points$[0])+"/"+(.@q[5]*.@q[1])+")^000000";
	if (.@q[6]) for(set .@i,6; .@i<getarraysize(.@q); set .@i,.@i+2)
		mes " > "+Chk(countitem(.@q[.@i]),.@q[.@i+1]*.@q[1])+((.ShowID)?"{"+.@q[.@i]+"} ":"")+Slot(.@q[.@i])+" ("+countitem(.@q[.@i])+"/"+(.@q[.@i+1]*.@q[1])+")^000000";
	next;
	setarray @qe[1], getiteminfo(.@q[0], ITEMINFO_LOC), getiteminfo(.@q[0], ITEMINFO_VIEWSPRITE);
	if (@qe[2] > 0 && ((@qe[1] & EQP_HEAD_LOW) || (@qe[1] & EQP_HEAD_TOP) || (@qe[1] & EQP_HEAD_MID) || (@qe[1] & EQP_COSTUME_HEAD_TOP) || (@qe[1] & EQP_COSTUME_HEAD_MID) || (@qe[1] & EQP_COSTUME_HEAD_LOW) || (@qe[1] & EQP_GARMENT) || (@qe[1] & EQP_COSTUME_GARMENT)))
		set .@preview,1;
	addtimer 1000, strnpcinfo(NPC_NAME)+"::OnEnd";
	while(1) {
		switch(select("[-] Purchase ^0055FF"+ getitemname(.@q[0]) +"^000000", "[-] Auto-loot Quest Items", ((.@preview && !@qe[7])?"[-] Headgear Preview...": ""), "[-] ^777777Cancel^000000")) {
		case 1:
			if (@shopeee==1 && BTS_Q <= 1) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes " ";
				mes "You need to finish break the seal quest first to be able to do this quest. Goodluck!";
				close;
			}
			if (@shopeee==2 && BTS_Q <= 1) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes " ";
				mes "You need to finish break the seal quest first to be able to do this quest. Goodluck!";
				close;
			}
            if (@shopeee==3 && BTS_2Q <= 1) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes " ";
				mes "You need to finish break the seal 2 quest first to be able to do this quest. Goodluck!";
				close;
			}
            if (@shopeee==4 && BTS_2Q <= 1) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes " ";
				mes "You need to finish break the seal 2 quest first to be able to do this quest. Goodluck!";
				close;
			}
			 if (@shopeee==5 && BTS_2Q <= 1) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes " ";
				mes "You need to finish break the seal 2 quest first to be able to do this quest. Goodluck!";
				close;
			}
			 if (@shopeee==6 && BTS_2Q <= 1) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes " ";
				mes "You need to finish break the seal 2 quest first to be able to do this quest. Goodluck!";
				close;
			}
			 if (@shopeee==7 && BTS_2Q <= 1) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes " ";
				mes "You need to finish break the seal 2 quest first to be able to do this quest. Goodluck!";
				close;
			}
			if (@qe[0]) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes "You're missing one or more quest requirements.";
				close;
			}
			if (.@q[6]) for(set .@i,6; .@i<getarraysize(.@q); set .@i,.@i+2){
				IsRentalBound(.@q[.@i]);
			}
			if (!checkweight(.@q[0],.@q[2])) {
				mes "[ ^996600Quest Shop^000000 ]";
				mes "^FF0000You need "+(((.@q[2] * getiteminfo(.@q[0], ITEMINFO_WEIGHT)) + Weight - MaxWeight) / 10)+" additional weight capacity to complete this trade.^000000";
				close;
			}
			if (.@q[4]) Zeny -= (.@q[4]*.@q[1]);
			if (.@q[5]) setd .Points$[0], getd(.Points$[0])-(.@q[5]*.@q[1]);
			if (.@q[6]) for(set .@i,6; .@i<getarraysize(.@q); set .@i,.@i+2)
				delitem .@q[.@i],.@q[.@i+1]*.@q[1];
			getitem .@q[0],.@q[2];
			bts_limit += 1;
			//if (.Announce) announce strcharinfo(PC_NAME)+" has created "+((.@q[2] > 1)?.@q[2]+"x "+getitemname(.@q[0]):A_An(getitemname(.@q[0])))+"!",bc_all|bc_blue;
			if (.Announce) bcshout strcharinfo(PC_NAME)+" has created "+((.@q[2] > 1)?.@q[2]+"x "+getitemname(.@q[0]):A_An(getitemname(.@q[0])))+"!",bc_all,0x00FFFF;
			specialeffect(EF_FLOWERLEAF, AREA, playerattached());
			close;
		case 2:
			atcommand "@alootid reset";
            // Auto-loot logic using @alootid
            for(set .@i,6; .@i<getarraysize(.@q); set .@i,.@i+2) {
                if (getitemname(.@q[.@i]) != "null") {
                    atcommand "@alootid +" + .@q[.@i];
                }
            }
            message strcharinfo(PC_NAME),"Auto-loot configuration for quest items completed.";
            close;
		case 3:
			setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS|PCBLOCK_EQUIP, true);
			setarray @qe[3], getlook(LOOK_HEAD_BOTTOM), getlook(LOOK_HEAD_TOP), getlook(LOOK_HEAD_MID), getlook(LOOK_ROBE), 1;
			if ((@qe[1] & 1) || (@qe[1] & 4096)) changelook LOOK_HEAD_BOTTOM, @qe[2];
			else if ((@qe[1] & 256) || (@qe[1] & 1024)) changelook LOOK_HEAD_TOP, @qe[2];
			else if ((@qe[1] & 512) || (@qe[1] & 2048)) changelook LOOK_HEAD_MID, @qe[2];
			else if ((@qe[1] & 4) || (@qe[1] & 8192)) changelook LOOK_ROBE, @qe[2];
			break;
		case 4:
			close;
		}
	}

OnEnd:
	if (@qe[7]) {
		changelook LOOK_HEAD_BOTTOM, @qe[3];
		changelook LOOK_HEAD_TOP, @qe[4];
		changelook LOOK_HEAD_MID, @qe[5];
		changelook LOOK_ROBE, @qe[6];
	}
	deletearray @qe[0],8;
	setpcblock(PCBLOCK_MOVE|PCBLOCK_SKILL|PCBLOCK_USEITEM|PCBLOCK_CHAT|PCBLOCK_IMMUNE|PCBLOCK_COMMANDS|PCBLOCK_EQUIP, false);
	end;

function Add {
	if (getitemname(getarg(1)) == "null") {
		debugmes "Quest reward #"+getarg(1)+" invalid (skipped).";
		return;
	}
	setarray .@j[0],getarg(2),getarg(3),getarg(4);
	for(set .@i,5; .@i<getargcount(); set .@i,.@i+2) {
		if (getitemname(getarg(.@i)) == "null") {
			debugmes "Quest requirement #"+getarg(.@i)+" invalid (skipped).";
			return;
		} else
			setarray .@j[.@i-2],getarg(.@i),getarg(.@i+1);
	}
	copyarray getd(".q_"+getarg(1)+"[0]"),.@j[0],getarraysize(.@j);
	npcshopadditem "btshop"+getarg(0),getarg(1),((.ShowZeny)?getarg(3):0);
	return;
}

function Chk {
	if (getarg(0) < getarg(1)) {
		set @qe[0],1;
		return "^FF0000";
	} else
		return "^61A302";
}

function Slot {
	set .@s$,getitemname(getarg(0));
	switch(.ShowSlot) {
		case 1: if (!getitemslots(getarg(0))) return .@s$;
		case 2: if (getiteminfo(getarg(0), ITEMINFO_TYPE) == IT_WEAPON || getiteminfo(getarg(0), ITEMINFO_TYPE) == IT_ARMOR) return .@s$+" ["+getitemslots(getarg(0))+"]";
		default: return .@s$;
	}
}

function A_An {
	setarray .@A$[0],"a","e","i","o","u";
	set .@B$, "_"+getarg(0);
	for(set .@i,0; .@i<5; set .@i,.@i+1)
		if (compare(.@B$,"_"+.@A$[.@i])) return "an "+getarg(0);
	return "a "+getarg(0);
}
}

function	script	btshop	{
	deletearray @i[0],getarraysize(@i);
	for(set .@i,0; .@i<getargcount(); set .@i,.@i+1)
		set @i[.@i],getarg(.@i);
	doevent "btsquest_shop::OnMenu";
	end;
}


// Dummy shop data -- copy as needed.
//============================================================
-	shop	btshop1	FAKE_NPC,909:-1
-	shop	btshop2	FAKE_NPC,909:-1
-	shop	btshop3	FAKE_NPC,909:-1
-	shop	btshop4	FAKE_NPC,909:-1
-	shop	btshop5	FAKE_NPC,909:-1
-	shop	btshop6	FAKE_NPC,909:-1
-	shop	btshop7	FAKE_NPC,909:-1