prontera,147,140,7	script	Compensation NPC	4_F_KAFRA1,{
	
	mes "[ Compensation NPC ]";
	mes "-------------------------------";
	mes "Hello "+str<PERSON><PERSON>fo(0)+"!";
	mes "Would you like to claim your rewards buddy?";
	menu "Maybe later..",-,"Yes please Master!",Lyes;
	close;
	
	Lyes:
		mes " ";
		getinventorylist();
		if(#Compe2025 > 6) {
			mes "You can only claim this reward once per account!";
		} else if(Weight > (MaxWeight/2) || @inventorylist_count > 50) {
			mes "Please make sure that you have enough slots in your inventory and you can actually carry weight!";
			close;
		} else {
			mes "Here you go, thanks again!";
			#Compe2025 = 7;

			getitembound 3149,10,1;
			getitembound 40055,10,1;
			getitembound 40059,10,1;
			getitembound 40063,10,1;
			getitembound 40067,10,1;
			getitembound 40071,10,1;
			getitembound 40075,10,1;
			getitembound 14601,10,1;
			getitembound 12424,10,1;
			getitembound 12412,2,1;
			//getitembound 3157,1,1;
			//getitembound 8206,1,1;
			getitembound 3189,1,1;
			getitembound 3190,1,1;
			getitembound 3875,1,1;
			getitembound 3876,1,1;
			dispbottom "You've received 5 Activity Points! Total: "+#ACTIVITYPOINTS;
			dispbottom "Enjoy your free 3 Days VIP! You can now use @vipstatus and @vipbuffs command.";
			setactivity(5);
			
			.@subdays = 3;
			if(gettimetick(2) < #vip_time) {
				set #vip_time, #vip_time + (86400 * .@subdays);
			} else {
				set #vip_time, gettimetick(2) + (86400 * .@subdays);
			}
		}
		close;

}