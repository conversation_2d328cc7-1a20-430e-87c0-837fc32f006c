//================= Hercules Configuration ================================
//=       _   _                     _
//=      | | | |                   | |
//=      | |_| | ___ _ __ ___ _   _| | ___  ___
//=      |  _  |/ _ \ '__/ __| | | | |/ _ \/ __|
//=      | | | |  __/ | | (__| |_| | |  __/\__ \
//=      \_| |_/\___|_|  \___|\__,_|_|\___||___/
//================= License ===============================================
//= This file is part of Hercules.
//= http://herc.ws - http://github.com/HerculesWS/Hercules
//=
//= Copyright (C) 2014-2018  Hercules Dev Team
//=
//= Hercules is free software: you can redistribute it and/or modify
//= it under the terms of the GNU General Public License as published by
//= the Free Software Foundation, either version 3 of the License, or
//= (at your option) any later version.
//=
//= This program is distributed in the hope that it will be useful,
//= but WITHOUT ANY WARRANTY; without even the implied warranty of
//= MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//= GNU General Public License for more details.
//=
//= You should have received a copy of the GNU General Public License
//= along with this program.  If not, see <http://www.gnu.org/licenses/>.
//=========================================================================
// Battle (Player) Configuration File
//=========================================================================
// Note 1: Value is a config switch (true/false)
// Note 2: Value is in percents (100 means 100%)
// Note 3: Value is a bit field.
//=========================================================================

// Players' maximum HP rate? (Default is 100)
hp_rate: 100

// Players' maximum SP rate? (Default is 100)
sp_rate: 100

// Whether or not cards and attributes of the left hand are applied to the right hand attack (Note 1)
// (It is true on official servers)
left_cardfix_to_right: true

// The amount of HP a player will respawn with, 0 is default.
// (Unit is in percentage of total HP, 100 is full heal of HP, 0 is respawn with 1HP total.)
restart_hp_rate: 0

// The amount of SP a player will respawn with, 0 is default.
// (Unit is in percentage of total SP, 100 is full heal of SP, 0 is respawn with 1SP total.)
restart_sp_rate: 0

// Can a normal player by-pass the skill tree? (Note 1)
player_skillfree: true

// When set to true, forces skill points gained from 1st class to be put into 1st class
// skills, and forces novice skill points to be put into the basic skill. (Note 1)
player_skillup_limit: false

// Quest skills can be learned? (Note 1)
// Setting this to true can open an exploit on your server!
quest_skill_learn: false

// When skills are reset, quest skills are reset as well? (Note 1)
// Setting this to true can open an exploit on your server!
// NOTE: If you have quest_skill_learn set to true, quest skills are always reset.
quest_skill_reset: false

// You must have basic skills to be able to sit, trade, form a party or create a chatroom? (Note 1)
basic_skill_check: true

// When teleporting, or spawning to a map, how long before a monster sees you if you don't move? (time is in milliseconds)
// That is, when you go to a map and don't move, how long before the monsters will notice you.
// When moving, attacking or doing similar actions, the effect ends instantly.
// Value is also affected by 'invincible_time_inc' mapflag
player_invincible_time: 5000

// When set to true, it prevent portal abuse for avoid hits. Official value is false.
fix_warp_hit_delay_abuse: false

// The time interval for HP to restore naturally. (in milliseconds)
natural_healhp_interval: 6000

// The time interval for SP to restore naturally. (in milliseconds)
natural_healsp_interval: 8000

// Automatic healing skill's time interval. (in milliseconds)
natural_heal_skill_interval: 10000

// The maximum weight for a character to carry when the character stops healing naturally. (in %)
natural_heal_weight_rate: 50

// Maximum atk speed. (Default 190, Highest allowed 199)
max_aspd: 196

// Same as max_aspd, but for 3rd classes. (Default 193, Highest allowed 199)
max_third_aspd: 196

// Maximum walk speed rate (200 would be capped to twice the normal speed)
max_walk_speed: 300

// Maximum HP. (Default is 1000000)
max_hp: 1500000

// Maximum SP. (Default is 1000000)
max_sp: 1250000

// Max limit of char stats. (agi, str, etc.)
max_parameter: 255

// Same as max_parameter, but for 3rd classes.
max_third_parameter: 255

// Same as max_parameter, but for extended classes (Ex. Super Novice, Kagero/Oboro, Rebellion).
max_extended_parameter: 255

// Same as max_parameter, but for summoner class
max_summoner_parameter: 255

// Same as max_parameter, but for baby classes.
max_baby_parameter: 255

// Same as max_parameter, but for baby 3rd's.
max_baby_third_parameter: 255

// Max armor def/mdef
// NOTE: This setting have no effect if server is run on Renewal Mode (RENEWAL)
// NOTE: does not affects skills and status effects like Mental Strength
// If weapon_defense_type is non-zero, it won't apply to max def.
// If magic_defense_type is non-zero, it won't apply to max mdef.
max_def: 99

// Def to Def2 conversion bonus. If the armor def/mdef exceeds max_def,
// the remaining is converted to vit def/int mdef using this multiplier
// (eg: if set to 10, every armor point above the max becomes 10 vit defense points)
over_def_bonus: 0

// Max weight carts can hold.
max_cart_weight: 20000

// Prevent logout of players after being hit for how long (in ms, 0 disables)?
prevent_logout: 10000

// When should the server prevent a player from logging out? Have no effect if prevent_logout is disabled. (Note 3)
// Official servers prevent players from logging out after attacking, casting skills, and taking damage.
// 0 = Players can always logout
// 1 = Prevent logout on login
// 2 = Prevent logout after attacking
// 4 = Prevent logout after casting skill
// 8 = Prevent logout after being hit
prevent_logout_trigger: 14

// Display the drained hp/sp values from normal attacks? (Ie: Hunter Fly card)
show_hp_sp_drain: true

// Display the gained hp/sp values from killing mobs? (Ie: Sky Deleter Card)
show_hp_sp_gain: true

// Show the critical bonus for katar class weapon in player status window?  On
// official server, the critical bonus from katar class weapon isn't display.
// (Default: false)
show_katar_crit_bonus: false

// If set, when A accepts B as a friend, B will also be added to A's friend
// list, otherwise, only A appears in B's friend list.
// NOTE: this setting only enables friend auto-adding; auto-deletion does not work yet
friend_auto_add: true

// Are simultaneous trade/party/guild invite requests automatically rejected?
invite_request_check: true

// Players' will drop a 'Skull' when killed?
// 0 = Disabled
// 1 = Dropped only in PvP maps
// 2 = Dropped in all situations
bone_drop: 1

// Do mounted (on Peco) characters increase their size
// 0 = no
// 1 = only Normal Classes on Peco have Big Size
// 2 = only Baby Classes on Peco have Medium Size
// 3 = both Normal Classes on Peco have Big Size
//	and Baby Classes on Peco have Medium Size
character_size: 0

// Idle characters can receive autoloot?
// Set to the time in seconds where an idle character will stop receiving
// items from Autoloot (0: disabled).
idle_no_autoloot: 0

// Minimum distance a vending/chat room must be from a NPC in order to be placed
// Default: 3 (0: disabled).
min_npc_vendchat_distance: 0

// If min_npc_vendchat_distance is enabled,
// can players vend/chat room nearby hidden npc? (Ie: FAKE_NPC/HIDDEN_WARP_NPC)
vendchat_near_hiddennpc: true

// Super Novice's fury is enabled to increments of 10%, such as at 10.0%, 20.0% - 80.0%, 90.0%
// Changing snovice_call_type config to 1 enables its use at 0%, for maxed super novices.
// default: 0
snovice_call_type: 1

// How the server should measure the character's idle time? (Note 3)
// 0x001 - Walk Request
// 0x002 - UseSkillToID Request ( targetted skill use attempt )
// 0x004 - UseSkillToPos Request ( aoe skill use attempt )
// 0x008 - UseItem Request ( including equip/unequip )
// 0x010 - Attack Request
// 0x020 - Chat Request ( whisper, party, guild, bg, etca )
// 0x040 - Sit/Standup Request
// 0x080 - Emotion Request
// 0x100 - DropItem Request
// 0x200 - @/#Command Request
// 0x400 - NPC Script Interaction
// Please note that at least 1 option has to be enabled.
// Be mindful that the more options used, the easier it becomes to cheat features that rely on idletime (e.g. checkidle()).
// Default: walk ( 0x1 ) + useskilltoid ( 0x2 ) + useskilltopos ( 0x4 ) + useitem ( 0x8 ) + attack ( 0x10 ) = 0x1F
idletime_criteria: 0x15

// Can players get ATK/DEF from refinements on costume/shadow equips?
// Default: yes (Official behavior not known)
costume_refine_def: true
shadow_refine_def: true
shadow_refine_atk: true

// Keep player facing direction after warping?
// Default: false (on official servers players always faces north)
player_warp_keep_direction: true
